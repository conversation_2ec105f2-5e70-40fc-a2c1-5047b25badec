# SPDX-License-Identifier: GPL-2.0

#
# This file is part of Lustre, http://www.lustre.org/
#

MANFILES =					\
	lfs.1					\
	lfs-changelog.1				\
	lfs-changelog_clear.1			\
	lfs-df.1				\
	lfs-fid2path.1				\
	lfs-find.1				\
	lfs-flushctx.1				\
	lfs-getdirstripe.1			\
	lfs-getname.1				\
	lfs-getsom.1				\
	lfs-getstripe.1				\
	lfs-heat_get.1				\
	lfs-heat_set.1				\
	lfs-hsm.1				\
	lfs-hsm_action.1			\
	lfs-hsm_clear.1				\
	lfs-hsm_set.1				\
	lfs-hsm_state.1				\
	lfs-ladvise.1				\
	lfs_migrate.1				\
	lfs-migrate.1				\
	lfs-mirror-copy.1			\
	lfs-mirror-create.1			\
	lfs-mirror-delete.1			\
	lfs-mirror-extend.1			\
	lfs-mirror-read.1			\
	lfs-mirror-resync.1			\
	lfs-mirror-split.1			\
	lfs-mirror-verify.1			\
	lfs-mirror-write.1			\
	lfs-mkdir.1				\
	lfs-path2fid.1				\
	lfs-pcc-detach.1			\
	lfs-pcc-state.1				\
	lfs-pcc.1				\
	lfs-project.1				\
	lfs-quota.1				\
	lfs-rmfid.1				\
	lfs-setdirstripe.1			\
	lfs-setquota.1				\
	lfs-setstripe.1

if MANPAGES
if UTILS
man_MANS = $(MANFILES)
endif
endif

CLEANFILES = *.aux *.tex *.log *.pdf
EXTRA_DIST = $(MANFILES)

all:
