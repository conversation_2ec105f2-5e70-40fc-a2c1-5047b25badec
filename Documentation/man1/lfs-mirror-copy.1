.TH LFS-MIRROR-COPY 1 2024-08-19 <PERSON><PERSON><PERSON> "Lustre User Utilities"
.SH NAME
lfs-mirror-copy \- copy a mirror's content to other mirror(s) of a mirrored file
.SH SYNOPSIS
.SY "lfs mirror copy"
.BR --read-mirror | -i
.I SRC_ID0
.BR --write-mirror | -o
.IR TGT_ID1 [, TGT_ID2 ,...]
.I MIRRORED_FILE
.YS
.SH DESCRIPTION
This command copies a mirror's content to other mirror(s) of a mirrored file,
the file is specified by the path name
.IR MIRRORED_FILE ,
the mirrors are specified by their mirror ID.
.SH OPTIONS
.TP
.BR -i ", " --read-mirror \fISRC_ID0
This option indicates the content of which mirror specified by
.I SRC_ID0
needs to be read. The
.I SRC_ID0
is the numerical unique identifier for a mirror.
.TP
.BR -o ", " --write-mirror \fITGT_ID1\fR[,\fITGT_ID2\fR,...]
This option indicates the content of which mirror(s) specified by mirror IDs
needs to be written. The mirror IDs are separated with comma.
If the mirror id
.B -1
is used here, it means that all mirrors other than
the read mirror are to be written.
.SH NOTES
Be ware that the written mirror(s) will be marked as non-stale mirror(s),
be careful that after using this command, you could get a file with non-stale
mirrors while containing different contents.
.SH EXAMPLES
Copy the content of mirror with mirror ID 1 to the mirrors with mirror ID 2 and
mirror ID 3 in /mnt/lustre/file1:
.RS
.EX
.B # lfs mirror copy -i1 -o2,3 /mnt/lustre/file1
.EE
.RE
.PP
Copy the content of mirror with mirror ID 1 to all other mirrors in \
/mnt/lustre/file1:
.RS
.EX
.B # lfs mirror copy -i1 -o-1 /mnt/lustre/file1
.EE
.RE
.SH AVAILABILITY
The
.B lfs mirror copy
command is part of the
.BR lustre (7)
filesystem package since release 2.13.0
.\" Added in commit v2_12_52-20-gc6e7c0788d
.SH SEE ALSO
.BR lfs (1),
.BR lfs-getstripe (1),
.BR lfs-mirror-create (1),
.BR lfs-mirror-extend (1),
.BR lfs-mirror-read (1),
.BR lfs-mirror-split (1),
.BR lfs-mirror-verify (1),
.BR lfs-mirror-write (1),
.BR lfs-setstripe (1)
