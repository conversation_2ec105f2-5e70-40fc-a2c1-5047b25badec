.TH LFS-MIRROR-READ 1 2024-08-20 <PERSON><PERSON><PERSON> "Lustre User Utilities"
.SH NAME
lfs-mirror-read \- read a mirror's content of a mirrored file
.SH SYNOPSIS
.SY "lfs mirror read"
.BR --mirror-id | -N
.IR MIRROR_ID
.RB [ --outfile | -o
.IR OUTPUT_FILE ]
.I MIRRORED_FILE
.YS
.SH DESCRIPTION
This command reads the content of a file's mirror, the file is specified by the
path name
.IR MIRRORED_FILE ,
the mirror is specified by its mirror ID
.IR MIRROR_ID .
.P
If an
.I OUTPUT_FILE
is specified, the content will be written to the file,
otherwise the standard output stream will be used.
.SH OPTIONS
.TP
.BR -N ", " --mirror-id " \fIMIRROR_ID"
This option indicates the content of which mirror specified by
.I MIRROR_ID
needs to be read. The
.I MIRROR_ID
is the numerical unique identifier for a mirror.
.TP
.BR -o ", " --outfile " \fIoutput_file"
The path name of the output file, if not specified, the standard output stream
will be used.
.SH EXAMPLES
Dump the content of mirror with mirror ID 3 for /mnt/lustre/file1 to STDOUT:
.RS
.EX
.B # lfs mirror read --mirror-id 3 /mnt/lustre/file1
.EE
.RE
.PP
Dump the content of mirror with mirror ID 2 for /mnt/lustre/file1 to file
/tmp/m2:
.RS
.EX
.B # lfs mirror read -N 2 -o /tmp/m2 /mnt/lustre/file1
.EE
.RE
.SH AVAILABILITY
The
.B lfs mirror read
command is part of the
.BR lustre (7)
filesystem package since release 2.12.0
.\" Added in commit v2_11_56_0-70-g14171e787d
.SH SEE ALSO
.BR lfs (1),
.BR lfs-getstripe (1),
.BR lfs-mirror-copy (1),
.BR lfs-mirror-create (1),
.BR lfs-mirror-delete (1),
.BR lfs-mirror-extend (1),
.BR lfs-mirror-resync (1),
.BR lfs-mirror-split (1),
.BR lfs-mirror-verify (1),
.BR lfs-mirror-write (1),
.BR lfs-setstripe (1)
