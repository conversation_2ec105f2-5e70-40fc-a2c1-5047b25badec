.TH LFS-GETNAME 1 2024-08-15 <PERSON><PERSON><PERSON> "Lustre User Utilities"
.SH NAME
lfs-getname \- retrieve the filesystem name and instance ID
.SH SYNOPSIS
.SY "lfs getname"
.RB [ --help | -h ]
.RB [ --instance | -i ]
.RB [ --fsname | -n ]
.RB [ --uuid | -u ]
.RI [ PATHNAME ...]
.YS
.SH DESCRIPTION
Print the filesystem name, instance ID, and mountpoint for all
Lustre mount points on the local client. If one or more
.I PATHNAME
arguments are provided, then only print the Lustre filesystem name
and instance ID for these mount points.
.SH OPTIONS
.TP
.BR -h ", " --help
Print usage message.
.TP
.BR -i ", " --instance
Only show the mount instance ID for each filesystem.  If
.B -i
is used with a single
.I PATHNAME
then the instance ID is printed without the mountpoint.
.TP
.BR -n ", " --fsname
Print only the Lustre filesystem name for each filesystem.  If
.B -n
is used with a single
.I PATHNAME
then the filesystem name is printed without the mountpoint.
.TP
.BR -u ", " --uuid
Print only the UUID for this client filesystem mountpoint. Note that 
this is not a common UUID suitable for mounting the filesystem with,
but rather a unique identifier for this specific mount instance. If
.B -u
is used with a single
.I PATHNAME
then the uuid is printed without the mountpoint.
.SH EXAMPLES
.EX
.B # lfs getname
myth-ffff880428703400 /myth
testfs-ffff937009271000 /mnt/testfs
.P
.B # lfs getname -n /mnt/testfs
testfs
.P
.B # lfs getname -i /mnt/testfs
ffff937009271000
.P
.B # lfs getname -u /mnt/testfs
78adbfe8-1c12-4bd2-a485-69afa166677e
.EE
.SH AVAILABILITY
.B lfs getname
is part of the
.BR lustre (7)
filesystem package since release 2.2.0
.\" Added in commit 2.1.53~17
.SH SEE ALSO
.BR lfs (1),
.BR llapi_getname (3),
.BR lustre (7)
