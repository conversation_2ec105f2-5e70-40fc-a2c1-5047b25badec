.TH LFS-GETSOM 1 2024-08-15 <PERSON><PERSON><PERSON> "Lustre User Utilities"
.SH NAME
lfs-getsom \- list file attributes that are stored on an MDS
.SH SYNOPSIS
.SY "lfs getsom"
.RB [ -s | -b | -f ]
.I FILE
.YS
.SH DESCRIPTION
This command lists file attributes that are stored on the MDS. It is called
with the full path and file name for a file on the Lustre file system. If no
flags are used, then all file attributes stored on the MDS will be shown.
.SH OPTIONS
.TP
.BR -s
Only show the size value of the SOM data for a given file.
.TP
.BR -b
Only show the blocks value of the SOM data for a given file.
.TP
.BR -f
Only show the flag value of the SOM data for a given file.Valid flags are:
.Ex
0 - Unknown or no SoM data, must get size from OSTs.
1 - Known strictly correct, FLR or DoM file (SoM guaranteed).
2 - Known stale - was right at some point in the past, but it is known \
(or likely) to be incorrect now (e.g. opened for write).
4 - Approximate, may never have been strictly correct, need to sync SOM \
data to achieve eventual consistency.
.EE
.SH AVAILABILITY
The
.B lfs getsom
command is part of the
.BR lustre (7)
filesystem package since release 2.12.0
.\" Added in commit 2.11.54~43
.SH SEE ALSO
.BR lfs (1),
.BR llsom_sync (8)
