.TH LFS-GETSTRIPE 1 2024-08-15 <PERSON><PERSON><PERSON> "Lustre User Utilities"
.SH NAME
lfs-getstripe \- Lustre client command to print layout parameters of a file
.SH SYNOPSIS
.SY "lfs getstripe"
.RB [ --component-count | --comp-count ]
.br
.RB [ --component-end | --comp-end | -E [ = [ +- ]\c
.I END_OFFSET
.RB [ KMGTPE ]]
.br
.RB [ --component-flags | --comp-flags [ =\c
.IR COMP_FLAGS ]]
.br
.RB [ --component-id | --comp-id
.RI [= COMP_ID ]\c
.RB | -I\c
.RI [ COMP_ID ]]
.br
.RB [ --component-start [ = [ +- ]\c
.I START_OFFSET\c
.RB [ KMGTPE ]]]
.br
.RB [ --extension-size | --ext-size | -z ]
.RB [ --directory | -d ]
.RB [ --fid | -F ]
.RB [ --generation | -g ]
.RB [ --help | -h ]
.RB [ --hex-idx ]
.RB [ --layout | -L ]
.RB [ --mdt | --mdt-index | -m ]
.RB [ --ost | -O \ \c
.IR OST_NAME ]
.RB [ --pool | -p ]
.RB [ --quiet | -q ]
.RB [ --recursive | -r ]
.RB [ --raw | -R ]
.RB [ --stripe-count | -c ]
.RB [ --stripe-index | -i ]
.RB [ --stripe-size | -S ]
.RB [ --mirror-count | -N ]
.RB [[ ! ] --mirror-index =[ +- ]\c
.I MIRROR_INDEX\c
.RB " | [" ! ] --mirror-id =[ +- ]\c
.IR MIRROR_ID ]
.RB [ --verbose | -v ]
.RB [ --yaml | -y ]
.RB [ --no-follow ]
.IR FILENAME | DIRECTORY " ..."
.YS
.SH DESCRIPTION
.B lfs getstripe
is used to list the layout/striping information for a given filename or
directory tree. By default the stripe_count, stripe_size, stripe_offset,
and allocated OST objects for each file will be shown. If you only want
specific layout information to be printed, then the
.BR --stripe-count ,
.BR --stripe-size ,
.BR --extension-size ,
.BR --stripe-index ,
.BR --layout ,
.BR --fid ,
.BR --generation ,
.BR --component-id ,
.BR --component-flags ,
.BR --component-count ,
.BR --component-start ,
.BR --component-end ,
.BR --pool
or
.BR --mirror-index
or
.BR --mirror-id
options, or equivalent single-character options, can be used without an
argument to return only the specified field(s).
.PP
You can limit the displayed content to one or more specific components or
mirror of a composite file layout by specifying the matching
parameter(s) for the
.BR --component-id ,
.BR --component-flags ,
.BR --component-start ,
.BR --component-end ,
.BR --mirror-index ,
or
.BR --mirror-id ,
or their single-character options. For single-character options, the
argument must follow the option without a space, and for long options an
.RB ' = '
sign must be used.
.PP
The default behavior when a directory is specified is to list the striping
information for all files within the specified directory (like
.RB ' "ls -l"
.IR DIRECTORY ').
.SH OPTIONS
.TP
.BR --component-count ", " --comp-count
Print only the number of components in the file's layout.
.TP
.BR -E ", " --component-end ", " --comp-end [[ +- ] \fIEND_OFFSET [ KMGTPE ]]
Print only the component end offset (in bytes) for the component(s).
If the component
.I end
offset is specified (with optional suffix for SI units), print only the
attributes of the component(s) with the given end offset. If
.BI + END_OFFSET
or
.BI - END_OFFSET
is used, print components with respectively a larger or smaller
.I END_OFFSET
offset.
.TP
.BR --component-flags ", " --comp-flags [ ^ ] \fICOMP_FLAG ,...
Print only the component flags. If
.I COMP_FLAG
is specified, print only components matching the specified
.I COMP_FLAG
set. If
.BI ^ COMP_FLAG
is used, print only components not matching
.IR COMP_FLAG .
Multiple flags may be specified, separated by commas. Valid flag names are:
.RS 1.2i
.TP
.B init
Component has been initialized (has allocated OST objects).
.TP
.B stale
Replicated (mirrored) components that do not have up-to-date data. Stale
components will not be used for read or write operations, and need to be
resynched using
.B lfs mirror resync
before they can be accessed again.
.TP
.B prefer
Replicated (mirrored) components that are preferred for read or write.
For example, because they are located on SSD-based OSTs, or are more
local on the network to clients.
.TP
.B prefrd
Replicated (mirrored) components that are preferred for read.
.TP
.B prefwr
Replicated (mirrored) components that are preferred for write.
.TP
.B nosync
Replicated (mirrored) components that do not resync using
.BR "lfs mirror resync" .
Files with the
.B nosync
flag will also
print the timestamp when the flag was set on the replica.
.RE
.TP
.BR -I ", " --component-id ", " --comp-id [ \fICOMP_ID ]
Print only the component COMP_ID number for the component(s). The file-unique
component COMP_ID is assigned as each component is created, and is not re-used.
The COMP_ID is
.B not
necessarily related to the offset of the component within the file, in
particular since replicated file layouts may have overlapping extents.
If
.I COMP_ID
is specified, then print only the fields for the matching component.
.TP
.BR --component-start ", " --comp-start [ +- ] \fISTART_OFFSET [ KMGTPE ]
Print only the component start offset (in bytes) for the component(s).
If the component
.I START_OFFSET
offset is specified (with optional suffix for SI units), print only the
attributes of the component(s) with the given starting offset. If
.BI + START_OFFSET
or
.BI - START_OFFSET
is used, print components with respectively a larger or smaller
.I START_OFFSET
offset.
.TP
.BR -d ", " --directory
Get striping information for only the specified directory, like
.RB ' "ls -d" '.
.TP
.BR -F ", " --fid
Show only the 128-bit unique Lustre File Identifier (FID).
.TP
.BR -g ", " --generation
Print only the layout generation number.
.TP
.BR -h ", " --help
Print usage message.
.TP
.BR --hex-idx
Print OST and MDT indexes in hexadecimal rather than decimal.
.TP
.BR --layout
Show only the file layout, which is one of:
.RS 1.2i
.TP
.B bad
Files that have an invalid layout and are inaccessible.
.TP
.B foreign
Files that have a foreign (non-Lustre/free format) component.
Such files may be inaccessible without external software integration.
.TP
.B hole
Files that are missing a data object,
possibly due to OST failure and layout reconstruction by LFSCK.
.TP
.B mdt
Files that store the first data component on the MDT holding the inode.
.TP
.B overstriped
Files have more than one stripe per OST for improved lock scalability.
.TP
.B raid0
Traditional Lustre RAID-0 striping format.
.TP
.B released
HSM-archived files that are not resident in the filesystem.
.RE
.TP
.BR -m ", " --mdt ", " --mdt-index
Show the MDT index on which the file or directory inode is located.
.TP
.BR -N ", " --mirror-count
Print the number of mirrors on the file.
.TP
.BR --mirror-index =[ +- ] \fIMIRROR_INDEX
Print only the components of
.IR MIRROR_INDEX-th
mirror,
based on the order that the mirror components are stored in the file layout.
The
.I MIRROR_INDEX
starts at 1. If
.BI + MIRROR_INDEX
or
.BI - MIRROR_INDEX
is used, print components of mirror(s) respectively later or earlier than
the
.IR MIRROR_INDEX -th
mirror.
.RS 1.2i
.TP
.B !
Negates the meaning. Using + before
.I MIRROR_INDEX
means mirror appears 'later than
.IR MIRROR_INDEX ',
- before
.I MIRROR_INDEX
means mirror appears 'earlier than
.I MIRROR_INDEX '.
If neither is used, it means 'equal to
.IR MIRROR_INDEX '.
.RE
.TP
.B --mirror-id =[ +- ] \fIMIRROR_ID
Print only the components of the mirror with ID of
.IR MIRROR_ID .
The mirror IDs are assigned to new mirrors as they are created,
but may not be sequential if some mirrors are removed. If
.BI + MIRROR_ID
or
.BI - MIRROR_ID
is used, print components of mirror(s) with respectively a larger or smaller
mirror ID of
.I MIRROR_ID
.
.RS 1.2i
.TP
.B !
Negates the meaning. Using
.B +
before
.I MIRROR_ID
means mirror with ID 'larger than
.IR MIRROR_ID ',
.B -
before
.I MIRROR_ID
means mirror with ID 'smaller than
.IR MIRROR_ID '.
If neither is used, it means 'equal to
.IR MIRROR_ID '.
.RE
.TP
.BR -O ", " --ost \ \fIOST_NAME
Print only file layouts and OST objects on the specified
.IR OST_NAME .
.TP
.BR -p ", " --pool
Print only the OST pool name on which the file was created.
.TP
.BR -q ", " --quiet
Print only allocated objects for each file, not other layout parameters.
.TP
.BR -R ", " --raw
Print layout information without substituting the filesystem's default values
for unspecified fields. If the file layout is not set, 0, 0, and -1 will be
printed for the stripe_count, stripe_size, and stripe_offset respectively.
.TP
.BR -r ", " --recursive
Recurse into all subdirectories.
.TP
.BR -c ", " --stripe-count
Print the number of stripes in the file. For composite files this is
the stripe count of the last initialized component.
.TP
.BR -i ", " --stripe-index
Print the starting OST index for the file layout.
.TP
.BR -S ", " --stripe-size
Print the stripe size in bytes. For composite files this is the stripe
size of the last initialized component.
.TP
.BR -z ", " --extension-size ", " --ext-size
Print the extension size in bytes. For composite files this is the extension
size of the first extension component.
.TP
.BR -v ", " --verbose
Also print the layout magic, FID sequence, FID object ID, and FID, in
addition to the normally-printed attributes.
.TP
.BR -y ", " --yaml
Always print the layout in YAML format, rather than only using this
format for composite files.
.TP
.BR --no-follow
Print the stripe information of symbolic link itself.
.SH EXAMPLES
List the detailed object allocation of the given file:
.RS
.EX
.B # lfs getstripe -v /mnt/lustre/file1
.EE
.RE
.PP
List the detailed information of only component with ID 2 of the given file:
.RS
.EX
.B # lfs getstripe -v -I2 /mnt/lustre/file1
.EE
.RE
.PP
Print the mirror(s) appearing later than the first mirror in the file:
.RS
.EX
.B # lfs getstripe --mirror-index=+1 /mnt/lustre/file1
.EE
.RE
.PP
Print the mirror(s) with mirror ID other than 2 in the file:
.RS
.EX
.B # lfs getstripe ! --mirror-id=2 /mnt/lustre/file1
.EE
.RE
.PP
Print only the component IDs for all the uninitialized components:
.RS
.EX
.B # lfs getstripe --component-flags=^init -I /mnt/lustre/file1
.EE
.RE
.PP
Print only the component(s) that are instantiated but not stale:
.RS
.EX
.B # lfs getstripe --component-flags=init,^stale -I /mnt/lustre/file1
.EE
.RE
.PP
List information of components in a file with extent end less than 64MiB:
.RS
.EX
.B # lfs getstripe -E-64M /mnt/lustre/file1
.EE
.RE
.PP
Print only the component start for the component with ID of 3:
.RS
.EX
.B # lfs getstripe -I3 --component-start /mnt/lustre/file1
.EE
.RE
.PP
Lists the information of the components of a file in YAML format:
.RS
.EX
.B # lfs getstripe --yaml /mnt/lustre/file1
.EE
.RE
.SH AVAILABILITY
.B lfs getstripe
is part of the
.BR lustre (7)
filesystem package since release 0.10.0
.\" Added in commit 0.9.1
.SH SEE ALSO
.BR lfs (1),
.BR lfs-find (1),
.BR lfs-getdirstripe (1),
.BR lfs-setstripe (1),
.BR lustre (7)
