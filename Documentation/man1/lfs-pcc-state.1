.TH LFS-PCC-STATE 1 2025-01-24 "Lustre" "Lustre User Utilities"
.SH NAME
lfs-pcc-state \- display current PCC state of file
.SH SYNOPSIS
.SY "lfs pcc state"
.IR FILE \ [...]
.YS
.SH DESCRIPTION
Display the current PCC cache state of
.I FILE
on the local client.
This shows several fields for each file.  The cache attach
.B type
is one of
.BR none ,
.BR readonly ,
or
.BR readwrite .
The
.B PCC_file
shows relative pathname of the file within the local cache filesystem.
The
.B open_count
is the number of processes that have the local cache file open, and
.B flags
shows additional file states, including
.B attaching
for files that are not fully copied into cache,
.SH EXAMPLES
Show the PCC cache state of
.BR file :
.EX
.RS
.B $ lfs pcc state /mnt/lustre/file
.RE
.EE
.SH AVAILABILITY
.B lfs pcc state
is part of the
.BR lustre (7)
filesystem package since release 2.13.0
.\" Added in commit v2_12_53-113-gf172b11688
.SH SEE ALSO
.BR lfs (1),
.BR lfs-pcc (1),
.BR lfs-pcc-detach (1),
.BR lfs-pcc-state (1),
.BR llapi_pcc_attach (3),
.BR lctl-pcc (8)
