.TH LFS-MIGRATE 1 2025-05-23 Lu<PERSON>re "Lustre User Utilities"
.SH NAME
lfs-migrate \- migrate files or directories between MDTs or OSTs.
.SH SYNOPSIS
.SY "lfs migrate"
.RB [ -h ]
.RB [ -v ]
.RI [ SETSTRIPE_OPTIONS " ... ]"
.IR FILES_FROM
.SY "lfs migrate"
.B -m
.I START_MDT_INDEX
.RB [ -cdHv ]
.I DIRECTORY
.YS
.SH DESCRIPTION
Migrate OST objects between OSTs for the file(s) specified by \fIFILES_FROM\fR,
or recursively migrate
.I DIRECTORY
and all inodes/directories therein between MDTs.

.SH OPTIONS
.SS OST MIGRATE OPTIONS
The
.B lfs migrate
command can be used for moving files from one (or more) OSTs to other
OSTs (e.g. for space balancing between OSTs, or to evacuate an OST for
hardware reasons), to change the stripe count or other layout parameters
of a file (e.g. to increase the bandwidth of a file by striping it over
multiple OSTs), or to move the file between different classes of storage
(e.g. SSD vs. HDD OSTs, or local vs. remote OSTs in different pools).
.IR FILES_FROM
may be:
.TP
.IR FILENAME " [...]"
File names are listed on command line.
Multiple file names are separated by space char.
.TP
.BR -0 ", " --null
Read file names from stdin by default. Each file name is followed by a NUL char.
Usually is used after a pipeline from \fBlfs find --print0\fR command.
.TP
.BR --files-from = \fILIST_FILE
Read file names from file \fILIST_FILE\fR. One line for each file name.
If \fILIST_FILE\fR is \fB-\fR, read from stdandard input.
If \fB--null\fR is also specified, file names are separated by a NUL char.

.P
In OST object migration mode, the command supports the same
.I SETSTRIPE_OPTIONS
listed in
.BR lfs-setstripe (1)
to specify the layout of the target file. The migrate command differs from
.B lfs setstripe
in that
.B lfs migrate
will copy the data from the existing file(s) using the new layout parameters
to the new OST(s). In contrast,
.B lfs setstripe
is used for creating new (empty) files with the specified layout.
For OST object migration, there additional options available:
.PP
If setstripe is restricted to specified users, this also applies to
.B lfs migrate.
See
.BR lfs-setstripe (1)
for details.
.TP
.BR -b ", " --block
Block access to the file by other applications during data migration
(default). This prevents other processes from accessing the file during
migration, which prevents data data writes to the old file objects from
being lost. This should be used if an OST needs to be completely emptied
prior to its removal, to ensure all requested files are migrated off the
OST.
.TP
.BR -D ", " --non-direct
Do
.B not
use
.B O_DIRECT
read and write operations when migrating a file. The
.B O_DIRECT
option avoids data copy from kernel buffers into userspace, which can
impose CPU and memory overhead on the copy operation, but makes read and
write operations synchronous. Using the
.B --non-direct
option uses buffered read/write operations, which may improve migration
speed at the cost of more CPU and memory overhead.
.IP
This option cannot be used on encrypted files when the encryption key is not
available. It will result in
.BR -ENOKEY .
.TP
.BR -F ", " --fid
FID(s) are provided from command line/stdin/
.I LIST_FILE
instead of file path(s).
Option
.BR --lustre-dir = \fILUSTRE_MOUNT_POINT
may be specified at the same time.
.TP
.BR -h ", " --help
Print usage message.
.TP
.BR --lustre-dir = \fILUSTRE_MOUNT_POINT
The mount point of Lustre file system on which FIDs are.
If there is only one mounted Lustre file system, this option is not necessary.
This option must be specified together with
.BR --fid .
.TP
.BR -n ", " --non-block
Abort migration if concurrent file access is detected. This can be
used with OST space balancing migration to avoid interfering with file
access by applications if there is not a requirement to migrate any
particular file to the new layout.
.TP
.BR --stats
This option enables progress updates every 5 seconds, in YAML format.
.TP
.BR --stats-interval =\fISTATS_INTERVAL
This option enables progress updates every
.I STATS_INTERVAL
seconds, in YAML format.
.TP
.BR -v ", " --verbose
Print each filename as it is migrated.
.TP
.BR -W ", " --bandwidth-limit ", " --bandwidth " \fIBANDWIDTH_MB\fR[" MG ]
Limit the read and write bandwidth a migrate job consumes to no more than
.I BANDWIDTH_MB
MiB/s. An optional suffix can be used to specify units in
.BR M ebibytes
or
.BR G ibibytes
per second.
.SS NOTES
.B lfs migrate
has a complementary
.B lfs_migrate
script which is used to provide extra functionality when migrating file
data between OSTs and has a separate man page. See
.BR lfs_migrate (1)
for details.
.SS MDT MIGRATE OPTIONS
.TP
.BR -m ", " --mdt-index=\fIMDT_INDEX [, \fIMDT_INDEX ,...]
The specified
.I DIRECTORY
.B and all subdirectories and inodes
will be migrated to the MDT with the specified
.IR MDT_INDEX .
This is useful if new MDTs have been added to a filesystem and existing user or
project directories should be migrated off old MDTs to balance the space usage
and future metadata workload. If
.I MDT_INDEX
is -1, the MDT index will be balanced by free space and inodes among
available MDTs. If multiple
.I MDT_INDEX
values are specified in a comma-separated list, then all
subdirectories will be
.B striped
across all of the specified MDT indices as if an equivalent
.BI -c N
option were given.
.TP
.BR -c ", " --mdt-count= \fICOUNT
All directories and subdirectories in the tree will be striped across
.I COUNT
MDTs, always using
.I MDT_INDEX
as the primary MDT for the directory. If
.I MDT_INDEX is
.B -1
then
.I COUNT
directory stripes will be chosen from MDTs proportional to the amount
of free inodes and space on each MDT. If multiple
.I MDT_INDEX
values are specified in a comma-separated list, then the number of specified
.I MDT_INDEX
values must match
.IR COUNT .
.TP
.BR -d ", " --directory
Only migrate the specified
.I DIRECTORY
and the non-directory inodes that are directly located within it.
Similar to
.RB ' "ls -d" '
and
.RB ' "lfs getstripe -d" '.
.TP
.BR --clear-fixed
Do not set LMV_HASH_FLAG_FIXED upon migrating the directory. It allows a
striped dir with only one stripe to be converted to a plain directory.
.TP
.BR -H ", " --mdt-hash= \fIHASH_TYPE
Use
.I HASH_TYPE
for the new directory layout.
.RS 1.2i
.TP
.B all_char (type 1)
Sum of ASCII characters modulo number of MDTs. This
provides weak hashing of the filename, and is suitable
for only testing or when the input is known to have
perfectly uniform distribution (e.g. sequential numbers).
.TP
.B fnv_1a_64 (type 2)
Fowler-Noll-Vo (FNV-1a) hash algorithm. This provides
reasonably uniform, but not cryptographically strong,
hashing of the filename. (default)
.TP
.B crush (type 3)
CRUSH hash algorithm. This is a consistent hash
algorithm, so minimum sub files need to relocate
during directory restripe.
.RE
.SS NOTES
Only the root user can migrate directories. Files that have been archived by
HSM or are currently opened will fail to migrate, user can run the same migrate
command again to finish migration when files are ready. Both inode and
directory entry will be migrated. During migration directory and sub files can
be accessed like normal ones, but the migration itself cannot be interrupted.
.PP
It is not currently possible to migrate files with an
.B mdt
component (Data-on-MDT, DoM). If it is necessary to migrate such files off
a particular MDT, they must first be migrated to have a non-DoM file layout
and then the inodes migrated separately. See
.B EXAMPLES
for details on how to migrate DoM files between MDTs.
.SS WARNING
Each migrated file or directory will have a new FID, and hence a new inode
number. As a consequence, files archived by Lustre HSM that depend on
the FID as the identifier in the HSM archive cannot currently be migrated.
Having a new inode number may also cause backup tools to consider the
migrated file(s) to be a new, and cause them to be backed up again.
.SH EXAMPLES
This migrates the data in
.B file1
into a new layout with 2 stripes:
.EX
.RS
.B # lfs migrate -c 2 /mnt/lustre/file1
.RE
.EE
.PP
This migrates the data in
.B file2
into a three component composite layout (number of stripes depends on
file size):
.EX
.RS
.B # lfs migrate -E 256M -c 1 -E 16G -c 4 -E eof -c 40 /mnt/lustre/file2
.RE
.EE
.PP
Recursively move the subdirectories and inodes contained in directory
.B remotedir
from its current MDT to MDT0000 and MDT0002. The
.B testremote
directory and all of its subdirectories will be striped across both MDTs:
.EX
.RS
.B # lfs migrate -m 0,2 testremote
.RE
.EE
.PP
Move
.B ./testremote
and the first level of sub files from their current MDT
to the MDT with index 0 and 2. Different from above case, the layout of
subdirectories under
.B ./testremote
won't be changed:
.EX
.RS
.B # lfs migrate -m 0,2 -d ./testremote
.RE
.EE
.PP
Set a default PFL layout (without any DoM component) on the directory
.BR topdir :
.EX
.RS
.B # lfs setstripe -E 256M -c 1 -E 16G -c 4 -E eof -c 40 topdir
.RE
.EE
then find and migrate all regular files that have an
.B mdt
component to copy the default layout from the specified
.BR topdir :
.EX
.RS
.B # lfs find dir -type f -L mdt -0 | lfs migrate -0 --copy topdir
.RE
.EE
and finally migrate the directory
.B topdir
and all files and subdirectories in that tree to MDT0002. This allows
migrating files with DoM components off an MDT:
.EX
.RS
.B # lfs migrate -m 2 topdir
.RE
.EE
.SH AVAILABILITY
The
.B lfs migrate
command is part of the
.BR lustre (7)
filesystem package since release 2.4.0
.\" lfs_setstripe_migrate added in commit v2_3_63_0-6-gead6f5b2b5)
.SH SEE ALSO
.BR lfs (1),
.BR lfs-getdirstripe (1),
.BR lfs_migrate (1),
.BR lfs-mkdir (1),
.BR lfs-setdirstripe (1),
.BR lfs-setstripe (1),
.BR lctl (8)
