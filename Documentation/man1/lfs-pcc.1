.TH LFS-PCC 1 2025-01-24 "Lustre" "Lustre User Utilities"
.SH NAME
lfs-pcc \- commands used to interact with the Persistent Client Cache (PCC).
.SH SYNOPSIS
.SY "lfs pcc attach"
.BR --id | -i
.IR "NUM FILE" ...
.SY "lfs pcc attach"
.BR --id | -i
.I NUM
.BR --mnt | -m
.IR "MNTPATH FID" ...
.SY "lfs pcc state"
.IR FILE ...
.YS
.SH DESCRIPTION
.B lfs pcc attach
.BR --id | -i
.IR "NUM FILE" ...
.IP
Attach given files on the persistent client cache. Use
.PP
.B lfs pcc detach
to remove the cached files from PCC either manually, or through automatic
mechanisms for the purpose of the cache space management.
.PP
.B lfs pcc attach
.BR --id | -i
.I NUM
.BR --mnt | -m
.IR "MNTPATH FID" ...
.IP
Attach given files into the persistent client cache by FID(s).
.PP
.B lfs pcc state
.IR FILE ...
.IP
Display the PCC state for given files.
.SH OPTIONS
.TP
.BR --id ", " -i
For RW-PCC, it is HSM ARCHIVE ID to choose which backend for cache files.
.TP
.BR --mnt ", " -m
Specify the Lustre mount point.
Before using RW-PCC, you need to configure HSM root and Archive ID mapping properly:
.B lfs pcc add $MNTPATH $PCCPATH \ "$PARAM"
Add one PCC backend to the Lustre client. For RW-PCC, when a file is being
created, a rule-based policy is used to determine whether it will be cached.
The rule expression supports logical conditional conjunction and disjunction
operations among different users, groups, projects, or filenames including
wildcards. You need to specify auto create caching rule and archive ID in
.B $PARAM.
On this client any subsequently created files matching the condition of auto
caching rule will be persistently cached automatically.
.TP
.B lfs pcc del $MNTPATH $PCCPATH
 Delete one PCC backend
.TP
.B lfs pcc clear $MNTPATH
 Clear and remove all PCC backends for the client.
.SH EXAMPLES
Enable HSM on the appropriate MDT:
.EX
.RS
.B # lctl set_param mdt.$FSNAME-MDT0000.hsm_control=enabled
.RE
.EE
.PP
Launch one copytool on client node to connect cache storage:
.EX
.RS
.B # lhsmtool_posix --daemon --hsm-root /mnt/pcc/ --archive=1 /mnt/lustre
.RE
.EE
.PP
Add HSM root and Archive ID (referenced by
.IB rwid
name-value pair) mapping for RW-PCC. Where "&" represents the logical
conjunction operator while "," represents the logical disjunction operator.
The example rule means that new files are only auto cached if the project ID is
either 500 or 1000 and the suffix of the file name is “h5” or the user ID is
1001:
.EX
.RS
.B # lfs pcc add /mnt/lustre /mnt/pcc \ "projid={500,1000}&fname={*.h5},uid=1001 rwid=1"
.RE
.EE
.PP
Attach an existing file into PCC and migrate data from lustre to Cache Device,
any I/O to the Lustre file will direct to the RW-PCC copy:
.EX
.RS
.B $ lfs pcc attach -i 1 /mnt/lustre/file
.RE
.EE
.PP
Attach an existing file referenced by FID "0x200000401:0x1:0x0" into PCC:
.EX
.RS
.B $ lfs pcc attach_fid -i 1 -m /mnt/lustre 0x200000401:0x1:0x0
.RE
.EE
.PP
Display the PCC state of the file "/mnt/lustre/file":
.EX
.RS
.B $ lfs pcc state /mnt/lustre/file
file: /mnt/lustre/file, type: readwrite, PCC file: /mnt/pcc/0004/0000/0bd1/0000/0002/0000/0x200000bd1:0x4:0x0, user number: 1, flags: 6
.RE
.EE
.PP
Display the PCC state of the file "/mnt/lustre/file":
.EX
.RS
.B $ lfs pcc state /mnt/lustre/file
file: /mnt/lustre/file, type: readwrite, PCC file: /mnt/pcc/0004/0000/0bd1/0000/0002/0000/0x200000bd1:0x4:0x0, user number: 1, flags: 6
.RE
.EE
.SH AVAILABILITY
.B lfs pcc attach
is part of the
.BR lustre (7)
filesystem package since release 2.13.0
.\" Added in commit v2_12_53-113-gf172b11688
.SH SEE ALSO
.BR lfs (1),
.BR lfs-hsm (1),
.BR lfs-pcc-detach (1),
.BR lctl-pcc (8)
