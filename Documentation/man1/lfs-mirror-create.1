.TH LFS-MIRROR-CREATE 1 2024-08-20 <PERSON><PERSON><PERSON> "Lustre User Utilities"
.SH NAME
lfs-mirror-create \- create a mirrored file or directory
.SH SYNOPSIS
.SY "lfs mirror create"
.BR --mirror-count | -N\c
.RI [ MIRROR_COUNT ]
.RI [ SETSTRIPE_OPTIONS |\c
.RB [ --flags\c
.RI = FLAGS "]] ..."
.IR FILENAME | DIRECTORY
.YS
.SH DESCRIPTION
This command creates a mirrored file or directory specified by the path name
.I FILENAME
or
.IR DIRECTORY .
.P
The
.BR --mirror-count | -N
parameter is required and indicates how many
mirrors that have the same layout will be created. It can be repeated multiple
times to separate mirrors that have different layouts. The
.I MIRROR_COUNT
argument is optional and defaults to 1 if it's not specified; if specified, it
must follow the option without a space.
.P
The
.I SETSTRIPE_OPTIONS
specify the specific layout for the mirror.
It can be a plain layout with specific striping pattern or a composite layout
like Progressive File Layout (PFL) (see
.BR lfs-setstripe (1)).
If
.I SETSTRIPE_OPTIONS
are not specified,
then the stripe options inherited from the previous component will be used. If
there is no previous component, then the
.I STRIPE_COUNT
and
.I STRIPE_SIZE
options inherited from filesystem-wide default values will be used, and OST
.I POOL_NAME
inherited from parent directory will be used.
.P
If no option is specified, then the command will return an error.
.SH OPTIONS
.TP
.BR -N ", " --mirror-count [\fIMIRROR_COUNT ]
The number of mirrors that have the same layout to be created. The option can be
repeated multiple times to separate mirrors that have different layouts. The
.I MIRROR_COUNT
argument is optional and defaults to 1 if it's not specified;
if specified, it must follow the option without a space.
.TP
.I SETSTRIPE_OPTIONS
The layout of one mirror. The options are the same as those for
.BR lfs-setstripe (1)
command.
If
.I SETSTRIPE_OPTIONS
are not specified,
then the stripe options inherited from the previous component will be used.
.TP
.BR --flags =\fIFLAGS
Where available
.I FLAGS
are as follows:
.RS
.TP
.B prefer
is supported in mirror creation. This flag will be set to all components that
belong to ths corresponding mirror. The
.B prefer
flag gives hint to Lustre for which mirrors should be used to serve I/O.
When a mirrored file is being read, the component(s) with
.B prefer
are likely to be picked to serve the read;
and when a mirrored file is prepared to be written,
the MDT will tend to choose the component with
.B prefer
flag set and stale the other components with overlapping extents.
This flag just provides a hint to Lustre,
which means Lustre may still choose mirrors without this flag set, for instance,
if all preferred mirrors are unavailable when the I/O occurs.
This flag could be set on multiple components.
.LP
Please note that this flag will be set to all components that belong to the
corresponding mirror. There also exists option
.B --comp-flags
that can be set to individual components at mirror creation time.
.RE
.SH EXAMPLES
Create a mirrored file with 2 mirrors. Each mirror has the same default striping
pattern with
.I STRIPE_COUNT
and
.I STRIPE_SIZE
inherited from filesystem-wide default values, and OST
.I POOL_NAME
inherited from parent directory:
.RS
.EX
.B # lfs mirror create -N2 /mnt/lustre/file1
.EE
.RE
.PP
Create a mirrored directory with 2 PFL mirrors.
Each mirror has the same specified PFL layout:
.RS
.EX
.B # lfs mirror create -N2 -E 1M -E eof -c -1 /mnt/lustre/dir1
.EE
.RE
.PP
Create a mirrored file with 3 PFL mirrors.
Each mirror has the same specified PFL layout:
.RS
.EX
.B # lfs mirror create -N3 -E 1M -c 1 -E 32M -c 4 -S 16M -E eof -c -1 \
/mnt/lustre/file1
.EE
.RE
.PP
Create a mirrored file with 3 plain layout mirrors. The first mirror has a
single stripe and 4MB stripe size. The second mirror has two stripes and locates
on OSTs with indices 2 and 3 allocated from the
.B flash
OST pool. It also has 4MB stripe size inherited from the first mirror.
The third mirror has two stripes and 4MB stripe size inherited from the previous
mirror, and also has inherited OST
.I POOL_NAME
from parent directory:
.RS
.EX
.B # lfs mirror create -N -c 1 -S 4M -N -c 2 -o 2,3 -p flash \
-N -p none /mnt/lustre/file1
.EE
.RE
.PP
Create a mirrored file with 5 PFL mirrors. The first and second mirrors have the
same PFL layout, and both of the components are allocated from the
.B flash
OST pool. Also, flag
.B prefer
is applied to all the components of the first
two mirrors, which tells the client to read data from those components whenever
they are available.
.P
The last three mirrors have the same PFL layout, and each of these
components have a stripe size of 16MB and use OSTs in the
.B archive
pool. However, the flag
.B prefer
is only applied to the first component of each mirror:
.RS
.EX
.B # lfs mirror create -N2 -E 4M -c 2 --pool flash --flags prefer -E eof -c 4 \
-N3 -E 16M -c 4 -S 16M --pool archive --comp-flags=prefer -E eof -c -1 \
/mnt/lustre/file1
.EE
.RE
.SH AVAILABILITY
The
.B lfs mirror create
command is part of the
.BR lustre (7)
filesystem package since release 2.11.0
.\" Added in commit v2_10_55_0-55-g125f98fb5c
.SH SEE ALSO
.BR lfs (1),
.BR lfs-mirror-delete (1),
.BR lfs-mirror-extend (1),
.BR lfs-mirror-resync (1),
.BR lfs-mirror-split (1),
.BR lfs-mirror-verify (1),
.BR lfs-setstripe (1)
