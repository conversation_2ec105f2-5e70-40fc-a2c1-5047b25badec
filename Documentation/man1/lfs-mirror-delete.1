.TH LFS-MIRROR-DELETE 1 2024-08-20 <PERSON><PERSON><PERSON> "Lustre User Utilities"
.SH NAME
lfs-mirror-delete \- delete a specified mirror from an existing mirrored file
.SH SYNOPSIS
.SY "lfs mirror delete"
.RB { --mirror-id
.I MIRROR_ID
|
.BR --component-id | --comp-id | -I
.I COMP_ID
|
.B --pool
.IR POOL }
.I MIRRORED_FILE
.YS
.SH DESCRIPTION
This command deletes a mirror with mirror ID
.I MIRROR_ID
or containing a component with
.I COMP_ID
or containing a component on
.I POOL
out of a mirrored file specified by the path name
.IR MIRRORED_FILE .
.P
If
.I MIRRORED_FILE
has only one mirror existing after split,
it will be converted to a regular non-mirrored file.
.P
If the original
.I MIRRORED_FILE
is not a mirrored file, then the command will return an error.
.SH OPTIONS
.TP
.BI --mirror-id " MIRROR_ID"
The numerical unique identifier for a mirror. The mirror ID is unique within a
mirrored file and is automatically assigned at file creation or extension time.
It can be fetched by
.B lfs getstripe
command (see
.BR lfs (1)).
.TP
.BR -I ", " --comp-id " \fICOMP_ID"
The numerical unique identifier for a component contained within a mirror.
.TP
.BI --pool " POOL"
The pool storing a component contained within a mirror.
.SH EXAMPLES
Delete the mirror with ID 2 of /mnt/lustre/file1:
.RS
.EX
.B # lfs mirror delete --mirror-id 2 /mnt/lustre/file1
.EE
.RE
.PP
Delete the mirror containing the component with ID 65537 of /mnt/lustre/file1:
.RS
.EX
.B # lfs mirror delete --comp-id 65537 /mnt/lustre/file1
.EE
.RE
.PP
Split a mirror on pool ssd out of /mnt/lustre/file1 and destroy it:
.RS
.EX
.B # lfs mirror delete --pool ssd /mnt/lustre/file1
.EE
.RE
.SH AVAILABILITY
The
.B lfs mirror delete
command is part of the
.BR lustre (7)
filesystem package since release 2.13.0
.\" Added in commit v2_12_58-138-g6d0e247cfc
.SH SEE ALSO
.BR lfs (1),
.BR lfs-mirror-create (1),
.BR lfs-mirror-extend (1),
.BR lfs-mirror-resync (1),
.BR lfs-mirror-split (1),
.BR lfs-mirror-verify (1),
.BR lfs-setstripe (1)
