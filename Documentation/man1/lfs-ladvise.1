.TH LFS-LADVISE 1 2024-08-30 <PERSON><PERSON><PERSON> "Lustre User Utilities"
.SH NAME
lfs-ladvise \- give file access advices or hints to server.
.SH SYNOPSIS
.SY "lfs ladvise"
.RB [ --advice | -a
.IR ADVICE ]
.RB [ --background | -b ]
.RB [ --start | -s
.I START\c
.RB [ kMGT ]]
.RB {[ --end | -e
.I END\c
.RB [ kMGT ]]
|
.RB [ --length | -l
.I LENGTH\c
.RB [ kMGT ]]}
.RB {[ --mode | -m
.IR MODE ]
|
.RB [ --unset | -u ]}
.IR FILE " ..."
.YS
.SH DESCRIPTION
Give file access advices or hints to Lustre server side, usually OSS. This lfs
command is similar to the Linux
.BR fadvise64 (2)
system call and
.BR posix_fadvise (2),
except it can forward the hints from Lustre clients to remote servers.
.SH OPTIONS
.TP
.BR -a ", " --advice= \fIADVICE
Give advice or hint of type
.I ADVICE
Advice types are:
.TP 15
.B willread
to prefetch data into server cache
.TP
.B dontneed
to cleanup data cache on server
.TP
.B lockahead
to request a lock on a specified extent of a file
.TP
.B locknoexpand
to disable server side lock expansion for a file
.TP 7
.BR -b ", " --background
Enable the advices to be sent and handled asynchronously.
.TP
.BR -s ", " --start= \fISTART_OFFSET
File range starts from
.I START_OFFSET.
.TP
.BR -e ", " --end= END_OFFSET
File range ends at (not including)
.I END_OFFSET.
This option may not be specified at the same time as the -l option.
.TP
.BR -l ", " --length= LENGTH
File range has length of
.I LENGTH.
This option may not be specified at the
same time as the -e option.
.TP
.BR -m ", " --mode= MODE
Specify the lock
.I MODE.
This option is only valid with lockahead
advice.  Valid modes are: READ, WRITE
.TP
.BR -u ", " --unset= UNSET
Unset the previous advice.  Currently only valid with locknoexpand advice.
.SH NOTES
Typically,
.B lfs ladvise
forwards the advice to Lustre servers without
guaranteeing how and when servers will react to the advice. Actions may or
may not be triggered when the advices are received, depending on the type of
the advice, whether the backing filesystem type supports that advice, as well
as the real-time decision of the affected server-side components.
.PP
A typical usage of ladvise is to enable applications and users with external
knowledge to intervene in server-side cache management. For example, if a
group of different clients are doing small random reads of a file, prefetching
pages into OSS cache with big linear reads before the random IO is a net
benefit. Fetching that data into each client cache with fadvise() may not
be a benefit if any individual client only reads a subset of the file.
.PP
The main difference between Linux fadvise() system call and ladvise is that
fadvise() is only a client side mechanism that does not pass the advice to the
filesystem, while ladvise can send advices or hints to Lustre server sides.
.SH EXAMPLES
Gives the OST(s) holding the first 1GB of
.B mnt/lustre/file1
a hint that the first 1GB of that file will be read soon:
.RS
.EX
.B # lfs ladvise -a willread -s 0 -e 1024M /mnt/lustre/file1
.EE
.RE
.PP
Gives the OST(s) holding the first 1GB of
.B /mnt/lustre/file1
a hint that the first 1GB of file will not be read in the near future,
thus the OST(s) could clear the cache of that file in the memory:
.RS
.EX
.B # lfs ladvise -a dontneed -s 0 -e 1G /mnt/lustre/file1
.EE
.RE
.PP
Request a read lock on the first 1 MiB of /mnt/lustre/file1:
.RS
.EX
.B # lfs ladvise -a lockahead -s 0 -e 1048576 -m READ /mnt/lustre/file1
.EE
.RE
Request a write lock on the first 4KiB of /mnt/lustre/file1:
.RS
.EX
.B # lfs ladvise -a lockahead -s 0 -e 4096 -m WRITE ./file1
.EE
.RE
Set disable lock expansion on ./file1:
.RS
.EX
.B # lfs ladvise -a locknoexpand ./file1
.EE
.RE
Unset disable lock expansion on ./file1:
.RS
.EX
.B # lfs ladvise -a locknoexpand -u ./file1
.EE
.RE
.SH AVAILABILITY
The
.B lfs ladvise
command is part of the
.BR lustre (7)
filesystem package since release 2.9.0
.\" Added in commit v2_8_51-30-ge14246641c
.SH SEE ALSO
.BR lfs (1),
.BR fadvise64 (2),
.BR posix_fadvise (2),
.BR llapi_ladvise (3),
.BR lustre (7)
