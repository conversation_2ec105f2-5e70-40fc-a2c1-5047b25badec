.TH LFS-FIND 1 2025-05-23" Lustre "Lustre User Utilities"
.SH NAME
lfs-find \- Lustre client utility to list files with specific attributes
.SH SYNOPSIS
.SY "lfs find"
.IR DIRECTORY | FILENAME " ..."
.br
.RB [[ ! ]
.BR --atime | -A
.RB [ -+ ]\c
.IR N [ smhdwy ]]
.br
.RB [[ ! ]
.BR --attrs =\c
.RI [ ^ ] ATTR [,...]]
.br
.RB [[ ! ]
.BR --blocks | -b
.RB [ +- ]\c
.IR N ]
.br
.RB [[ ! ]
.BR --btime | -B | --crtime
.RB [ +- ]\c
.IR N [ smhdwy ]]
.br
.RB [[ ! ]
.BR --ctime | -C
.RB [ +- ]\c
.IR N [ smhdwy ]]
.br
.RB [[ ! ]
.BR --component-count | --comp-count
.RB [ +- ]\c
.IR N ]
.br
.RB [[ ! ]
.BR --component-end | --comp-end | -E
.RB [ +- ]\c
.I N\c
.RB [ KMGTPE ]]
.br
.RB [[ ! ]
.BR --component-flags | --comp-flags
.RI [ ^ ] FLAG ", ...]"
.br
.RB [[ ! ]
.BR --component-start | --comp-start
.RB [ +- ]\c
.I N\c
.RB [ KMGTPE ]]
.br
.RB [[ ! ]
.BR --extension-size | -z
.RB [ +- ]\c
.I N\c
.RB [ KMG ]]
.br
.RB [[ ! ]
.B --foreign
.RI [ TYPE ]]
.br
.RB [[ ! ]
.BR --gid | -g | --group | -G
.IR GNAME | GID ]
.RB [ --help | -h ]
.br
.RB [[ ! ]
.BR --layout | -L
.BR mdt | raid0 | released ]
.RB [ --lazy | -l ]
.RB [ --ls ]
.br
.RB [[ ! ]
.B --links
.RB [ +- ]\c
.IR N ]
.RB [ --maxdepth | -D
.IR N ]
.RB [ --mindepth | -d
.IR N ]
.br
.RB [[ ! ]
.BR --mdt | --mdt-index | -m
.IR UUID | INDEX | INDEX_RANGE ", ...]"
.br
.RB [[ ! ]
.BR --mdt-count | -T
.RB [ +- ]\c
.IR N]
.br
.RB [[ ! ]
.BR --mdt-hash | -H
.RI [ ^ ] HASHFLAG ,[ ^ ] HASHTYPE ", ...]"
.br
.RB [[ ! ]
.BR --mirror-count | -N
.RB [ +- ]\c
.IR N ]
.RB [[ ! ]
.B --mirror-state
.RI [ ^ ] STATE ]
.br
.RB [[ ! ]
.BR --mtime | -M
.RB [ -+ ]
.IR N [ smhdwy ]]
.RB [[ ! ]
.BR --name | -n
.IR PATTERN ]
.br
.RB [[ ! ]
.BR --newer [ XY ]
.IR REFERENCE ]
.RB [[ ! ]
.BR --ost | -O
.IR INDEX | INDEX_RANGE ", ...]"
.br
.RB [[ ! ]
.B --perm
.RB [ /- ]
.IR MODE ]
.RB [[ ! ]
.B --pool
.IR POOL ]
.RB [ --print | -P ]
.br
.RB [ --print0 | -0 ]
.RB [ --printf
.IR FORMAT ]
.RB [[ ! ]
.B --projid
.IR PROJID | PROJNAME ]
.br
.RB [[ ! ]
.BR --size | -s
.RB [ -+ ]\c
.I N\c
.RB [ KMGTPE ]]
.RB [ --skip | -k]
.IR PERCENT ]
.br
.RB [[ ! ]
.BR --stripe-count | -c
.RB [ +- ]\c
.IR N ]
.br
.RB [[ ! ]
.BR --stripe-index | -i
.IR n ", ...]"
.RB [[ ! ]
.RB --stripe-size | -S
.RB [ +- ]\c
.I N\c
.RB [ KMG ]]
.br
.RB [[ ! ]
.BR --type | -t
.RB { bcdflps }]
.RB [[ ! ]
.BR --uid | -u | --user | -U
.IR UNAME | UID ]
.br
.RB [[ ! ]
.BR --xattr
.IR NAME [= VALUE ]]
.YS
.SH DESCRIPTION
.B lfs find
is similar to the standard
.BR find (1)
utility and is used to list files and directories with specific attributes,
both regular POSIX attributes such as ownership, timestamps, and size using
the same options as
.BR find (1),
as well as Lustre-specific attributes such as stripe count and size,
OST and MDT location, and composite layout attributes.
.SH OPTIONS
.TP
.BR -A ", " --atime
File was last accessed
.IR N *24
hours ago (if no units are given),
or
.IR N *\c
.BR s econds,
.BR m inutes,
.BR h ours,
.BR d ays,
.BR w eeks,
or
.BR y ears
ago within a margin of error of 24h if no unit is specified.
Multiple units can be specified, for example
.B 8h20m
is equivalent to
.BR  500m .
If multiple units are specified,
the margin of error is based on the smallest unit used, so
.B -atime 3d
has a margin of error of one day, while
.B -atime 72h
has a margin of error of one hour. If both
.BI "-atime +" OLDER
and
.BI "-atime -" NEWER
are used, then files with atime within the range
.IR OLDER - NEWER
are matched.
.TP
.BR --attrs
File has ATTRS attribute flags. Supported attributes are (non exhaustive list):
Compressed (c), Immutable (i), Append_Only (a), No_Dump (d), Encrypted (E),
Automount (M)
.TP
.BR -b ", " --blocks
Blocks allocated by the file is
.I N
512-byte
.BR b locks
(if no units are given), or
.BR c hars
(bytes),
.BR K ibi-,
.BR M ebi-,
.BR G ibi-,
.BR T ebi-,
.BR P ebi-,
or
.BR E bi-bytes
if that suffix is given.
.TP
.BR -B ", " --btime ", " --Btime ", " --crtime
File was created
.IR N *24
hours ago, see
.B --atime
for full details and options.
.TP
.BR -C ", " --ctime
File's status was last changed
.IR N *24
hours ago, see
.B --atime
for full details and options.
.TP
.BR --component-count ", " --comp-count
The file has
.I N
components in its layout.
.TP
.BR --component-end ", " --comp-end
The file has component end offset
.I N
(in bytes) for any component.
.TP
.BR --component-flags ", " --comp-flags
The file has components with the specified
.I flag
set. If
.BI ^ flag
is used, print only components not matching
.IR flag .
Multiple flags can be specified, separated by commas. Valid flag names are:
.RS 1.2i
.TP
.B init
Component has been initialized (has allocated OST objects).
.TP
.B stale
Replicated (mirrored) components that do not have up-to-date data. Stale
components will not be used for read or write operations, and need to be
resynched using
.B lfs mirror resync
before they can be accessed again.
.TP
.B prefer
Replicated (mirrored) components that are preferred for read or write.
For example, because they are located on SSD-based OSTs, or are more
local on the network to clients.
.TP
.B prefrd
Replicated (mirrored) components that are preferred for read.
.TP
.B prefwr
Replicated (mirrored) components that are preferred for write.
.RE
.TP
.BR --component-start ", " --comp-start
The file has component start offset
.I N
(in bytes) for any component.
.TP
.BR --foreign
File has a foreign (non-Lustre/free format) layout and is of the given
.IR TYPE ,
if specified. Presently only
.B none
or
.B symlink
are defined types, though 32-bit numeric types can also be used.
.TP
.BR -g ", " --gid
File has specified numeric group ID
.IR GID .
.TP
.BR -G ", " --group
File belongs to specified
.IR GROUPNAME ,
or numeric group
.IR GID .
.TP
.BR -h ", " --help
Print usage message.
.TP
.BR -L ", " --layout
File has a layout of the given type, one of:
.RS 1.2i
.TP
.B raid0
Traditional Lustre RAID-0 striping format.
.TP
.B released
HSM-archived files that are not resident in the filesystem.
.TP
.B mdt
Files that have the first data component on an MDT.
.RE
.TP
.BR -l ", " --lazy
Use file size and blocks from MDT, if available, to avoid extra RPCs.
.TP
.BR --ls
Print files in a format similar to
.RB ' "ls -l" ',
showing the inode number, allocated blocks, permissions, link count,
owner, group, size, file type, and path.
This is equivalent to specifying the
.B \'--printf '%i\\\\t%k\\\\t%M\\\\t%n\\\\t%u\\\\t%g\\\\t%s\\\\t%t\\\\t%p\\\\n'
option.
.TP
.BR --links
File has
.I N
links.
.TP
.BR --maxdepth
Limits find to decend at most
.I N
levels of directory tree.
.TP
.BR --mindepth
Starts find at
.I N
levels of directory tree.
.TP
.BR -m ", " --mdt ", " --mdt-index
File or directory inode is located on the specified MDT(s).
.TP
.BR -H ", " --mdt-hash
DNE striped directory uses the given filename hashing function, one of:
.RS 1.2i
.TP
.B crush
The CRUSH consistent hash function, added in Lustre 2.14, minimizes
entry migration if the directory stripe count changes during migration.
.TP
.B fnv_1a_64
The Fowler\-Noll\-Vo hash function, which is a simple and efficient hash.
.TP
.B all_char
Simple hash function that sums all of the characters in the filename.
This is mostly for testing, or if it is known that filenames will use
sequential filenames.
.RE
This implicitly selects only directories to be matched, like
.B -type d
and not other file types.
.TP
.BR -T ", " --mdt-count
The DNE striped directory has the given number of MDT shards. This
implicitly selects only directories to be matched, like
.B -type d
and not other file types.
.TP
.BR -N ", " --mirror-count
The file has
.I N
mirrors in its layout.
.TP
.BR --mirror-state
The file has a state of
.I state.
If
.BI ^ state
is used, print only files not matching
.IR state.
Only one state can be specified. Valid state name is:
.RS 1.2i
.TP
.B ro
The mirrored file is in read-only state. All of the mirrors contain
the up-to-date data.
.TP
.B wp
The mirrored file is in a state of being written.
.TP
.B sp
The mirrored file is in a state of being resynchronized.
.RE
.TP
.BR -M ", " --mtime
File's data was last modified
.IR N *24
hours ago, see
.B --atime
for full details and options.
.TP
.BR -n ", " --name
Filename matches the given filename, or regular expression using
standard
.BR glob (7)
file name regular expressions and wildcards.
.TP
.BR --newer [ XY "] " \fIreference
Succeeds if timestamp
.I X
of the file being considered is newer
than timestamp
.I Y
of the file
.IR reference .
The letters
.I X
and
.I Y
can be any of the following letters:
.TP 4
.B a
The access time of the file
.I reference
.TP
.B b|B
The birth time of the file
.I reference
.TP
.B c
The inode status change time of
.I reference
.TP
.B m
The modification time of the file
.I reference
.TP
.B t
.I reference
is interpreted directly as a time
.PP
Some combinations are invalid; for example, it is invalid for
.I X
to be
.IR t .
Specifying
.B -newer
is equivalent to
.BR -newermm .
When
.IR reference
is interpreted directly as a time,
currently it must be in one of the following formats:
"%Y-%m-%d %H:%M:%S", "%Y-%m-%d %H:%M", "%Y-%m-%d", "%H:%M:%S", "%H:%M",
to represent year, month, day, hour, minute, seconds,
with unspecified times at the start of that minute or day,
unspecified dates being "today",
and "@%s" or "%s" the seconds since the Unix epoch (see
.BR strftime (3)
for details of the time formats). Otherwise, it will report an error.
If you try to use the birth time of a reference file,
and the birth time cannot be determined, a fatal error message results.
If you specify a test which refers to the birth time of files being examined,
this test will fail for any files where the birth time is unknown.
.TP
.BR -O ", " --ost
File has an object on the specified OST(s).
The OST names can be specified using the whole OST target name,
or just the OST index number, or OST index range like 0-3.
If multiple OSTs are given in a comma-separated list,
the file may have an object on any of the given OSTs.
Specifying multiple OSTs allows scanning the filesystem only once
when migrating objects off multiple OSTs for evacuation and replacement using
.BR lfs-migrate (1).
.TP
.BI --perm " MODE"
File's permission are exactly
.I MODE
(octal or symbolic).
.TP
.BR --perm /\fIMODE
All of the permission bits
.I MODE
are set for the file.
.TP
.BR --perm -\fIMODE
Any of the permission bits
.I MODE
are set for the file. If no permission bits in
.I MODE
are set, this test matches any file.
.TP
.BR --pool
Layout was created with the specified
.I POOL
name. For composite files, this may match the pool of any component.
.BR -P ", " --print
Prints the file or directory name to standard output if it matches
all specified parameters, one file per line with a trailing linefeed.
This is the default behaviour for any matching files.
.TP
.BR -0 ", " --print0
Print full file name to standard output if it matches the specified
parameters, followed by a NUL character. This is for use together with
.BR xargs (1)
with the
.B -0
option to handle filenames with embedded spaces or other special characters.
.TP
.BI --printf " FORMAT"
Print
.I FORMAT
to standard output for each matching file, interpreting
`\' escapes and `%' directives. Unlike
.BR --print ,
the
.B --printf
option does not automatically add a newline to the end of the string.
The escapes and directives are:
.RS 1.2i
.TP
.B \en
Newline.
.TP
.B \et
Horizontal tab.
.TP
.B \e\e
A literal backslash.
.TP
.B %%
A literal percent sign.
.TP
.B %a
File\'s last access time in the format returned by the C \`ctime\' function.
.TP
.B %A@
File\'s last access time in seconds since Jan. 1, 1970, 00:00 GMT.
.TP
.B %b
The amount of disk space used for the file (in 512-byte blocks).
.TP
.B %c
File\'s last status change time in the format
returned by the C \`ctime\' function.
.TP
.B %C@
File\'s last status change time in seconds since Jan. 1, 1970, 00:00 GMT.
.TP
.B %g
File\'s groupname.
.TP
.B %G
File\'s numeric group ID.
.TP
.B %i
File\'s inode number in decimal.
.TP
.B %k
The amount of storage space allocated for this file in 1K blocks.
.TP
.B %m
File permission bits (in octal).
.TP
.B %M
File permissions (rwx format)
.TP
.B %n
Number of hard links to file.
.TP
.B %p
File's pathname name.
.TP
.B %s
File size in bytes.
.TP
.B %t
File\'s last modification time in the format
returned by the C \`ctime\' function.
.TP
.B %T@
File\'s last modification time in seconds since Jan. 1, 1970, 00:00 GMT.
.TP
.B %u
File's username.
.TP
.B %U
File's numeric user ID.
.TP
.B %w
File\'s birth time in the format returned by the C \`ctime\' function.
.TP
.B %W@
File\'s birth time in seconds since Jan. 1, 1970, 00:00 GMT.
.TP
.B %y
File's type (f=file, d=directory, p=pipe, b=block device, c=character device,
s=socket l=symbolic link)
.TP
Lustre-specific information about a file can be printed using these directives:
.TP
.B %La
Comma-separated list of file's named attribute flags in short form (letter), or
hex value of any unknown attributes.
.RE
.TP
.B %LA
Comma-separated list of file's named attribute flags, or hex value of any
unknown attributes.
.RE
.TP
.B %Lc
File or directory stripe count. For a composite file,
this is the stripe count of the last instantiated component.
.TP
.B %LF
File Identifier (FID) associated with the file.
.TP
.B %Lh
Directory's hash type (or \`none\' for an unstriped directory).
.TP
.B %Li
File\'s starting OST index (or starting MDT index for a directory).
For a composite file, this is the starting OST index of the last instantiated
component.
.TP
.B %Lo
List of all OST/MDT indices associated with a file or directory.
.TP
.B %Lp
File\'s OST pool name. For a composite file, this is the pool associated
with the last instantiated component. (NOTE: This can also be used for
directories, but since MDT pools are not currently implemented, nothing will
be printed.)
.TP
.B %LP
Numeric project ID assigned to the file or directory.
.TP
.B %LS
File's stripe size. For a composite file, this is the stripe size of the last
instantiated component.
.TP
.BR --projid
File belongs to the project specified by
.I PROJID
or
.IR PROJNAME .
A username or group name can be used in place of a project name by specifying
.BI u: USERNAME
or
.BI g: GROUPNAME
respectively.
.TP
.BR -s ", " --size
File size is
.I N
512-byte blocks (if no unit is given), or
.I N
.BR c hars
(bytes),
.BR K ibi-,
.BR M ebi-,
.BR G ibi-,
.BR T ebi-,
.BR P ebi-,
or
.BR E bi-bytes
if a suffix is given.
.TP
.BR -k ", " --skip
Skip
.I PERCENT
of the total files that match the other filters.
This is useful together with
.BR lfs-migrate (1)
to allow processing only a fraction of the files to rebalance files when new
OSTs are added to the filesystem.
.TP
.BR -c ", " --stripe-count
File has
.I N
stripes allocated. For composite files, this
matches the stripe count of the last initialized component.
.TP
.BR -i ", " --stripe-index
File has stripe on OST index
.IR N .
Multiple OST indices can be specified in a comma-separated list,
which indicates that the file has a stripe on \
.B any
of the specified OSTs. This allows a
single namespace scan for files on multiple different OSTs, if there
are multiple OSTs that are being replaced.
.TP
.BR -S ", " --stripe-size
Stripe size is
.I N
bytes, or
.BR K ibi-,
.BR M ebi-,
.BR G ibi-,
.BR T ebi-,
.BR P ebi-,
or
.BR E bi-abytes
if a suffix is given. For composite files,
this matches the stripe size of the last initialized non-extension component.
.TP
.BR -z ", " --extension-size ", " --ext-size
Extension size is
.I N bytes, or
.BR K ibi-,
.BR M ebi-,
.BR G ibi-,
.BR T ebi-,
.BR P ebi-,
or
.BR E bi-bytes
if a suffix is given.
For composite files, this matches the extension size of any extension component.
.TP
.BR -t ", " --type
File has type:
.BR b lock,
.BR c haracter,
.BR d irectory,
.BR f ile,
.BR p ipe,
.RB sym l ink,
or
.BR s ocket.
.TP
.BR -u ", " --uid
File has specified numeric user ID
.IR UID .
.TP
.BR -U ", " --user
File owned by specified
.IR USERNAME ,
or numeric user
.IR UID .
.TP
.BI --xattr NAME\fR[ = VALUE \fR]
File has an extended attribute with name matching the regular expression
.RB ( regex (7))
.IR NAME ,
and optionally value matching the regular expression
.IR VALUE .
The regular expressions must match the complete attribute names and values,
and not just a substring.
This option may be specified multiple times, and the file must match all
provided arguments.
.SH NOTES
Specifying
.B !
before an option negates its meaning
.RI ( "files NOT matching the parameter" ).
Using
.B +
before a numeric value means 'more than
.IR N ',
while
.B -
before a numeric value means 'less than
IR N '.
If neither is used, it means 'equal to
.IR N ',
within the bounds of the unit specified (if any).
.PP
Numeric suffixes are in binary SI (power-of-two) units.
.PP
For compatibility with
.BR find (1)
it is possible to specify long options with either a single or double
leading dash.
.PP
The order of parameters does not affect how the files are matched.
.B lfs find
will first scan the directory for any specified filename, and then fetch
MDT inode attributes for each matching filename. If it can make a
positive or negative decision for a file based only on the MDT attributes
(e.g. newer than specified time, user/group/project ID) it will not fetch
the OST object attributes for that file.
.SH BUGS
The
.B lfs find
command isn't as comprehensive as
.BR find (1).
In particular, it doesn't support complex boolean expressions with
.B -o
(logical OR), only logical AND of all expressions. The order that parameters
are specified does not affect how the files are matched.
.SH EXAMPLES
Efficiently lists all files in a given directory and its subdirectories,
without fetching any file attributes:
.EX
.RS
.B # lfs find /mnt/lustre
.RE
.EE
.PP
Prints a formatted find string, listing the username, access mode and inode
number:
.EX
.RS
.B # lfs find /mnt/lustre -printf "%u %M %i \n"
.RE
.EE
.PP
Recursively list all regular files in given directory more than 30 days old:
.EX
.RS
.B # lfs find /mnt/lustre -mtime +30 -type f -print
.RE
.EE
.PP
Recursively find all files in
.B test
that have objects on OST0002 or OST0003 and migrate them to other OSTs. See
.BR lfs_migrate (1)
for more details:
.EX
.RS
.B # lfs find /mnt/lustre/test -o OST0002,OST0003 -print0 | lfs_migrate -y
.RE
.EE
.PP
Recursively list all files ending with
.B .mpg
that have more than 3 components:
.EX
.RS
.B # lfs find -name "*.mpg" --component-count +3 /mnt/lustre
.RE
.EE
.PP
Recursively list all files that have at least one component with both 'init'
and 'prefer' flags set, and doesn't have flag 'stale' set:
.EX
.RS
.B # lfs find --component-flags=init,prefer,^stale /mnt/lustre
.RE
.EE
.PP
Recursively list all mirrored files that have more than 2 mirrors:
.EX
.RS
.B # lfs find --mirror-count +2 /mnt/lustre
.RE
.EE
.PP
Recursively list all out-of-sync mirrored files:
.EX
.RS
.B # lfs find ! --mirror-state=ro /mnt/lustre
.RE
.EE
.PP
Recursively list all but foreign files/dirs of
.B symlink
type:
.EX
.RS
.B # lfs find ! --foreign=symlink /mnt/lustre
.RE
.EE
.PP
Recursively list all files with the specified "user.job" extended attribute:
.EX
.RS
.B # lfs find -xattr user.job=202310.* /mnt/lustre
.RE
.EE
.PP
Recursively list all files in /var/www that have any SELinux extended attribute,
but that do NOT have an SELinux extended attribute with a value containing
"httpd":
.EX
.RS
.B # lfs find -xattr security.selinux ! -xattr security.selinux=.*httpd.* \
/var/www
.RE
.EE
.PP
Recursively list all files in a directory with detailed information in a format
similar to 'ls -l':
.EX
.RS
.B # lfs find /mnt/lustre --ls
.RE
.EE
.SH AVAILABILITY
.B lfs find
is part of the
.BR lustre (7)
filesystem package since release 0.10.0
.\" Added in commit 0.9.1
.SH SEE ALSO
.BR lfs (1),
.BR lfs-getdirstripe (1),
.BR lfs-getstripe (1),
.BR lfs-migrate (1),
.BR xargs (1),
.BR projid (5),
.BR lustre (7),
.BR regex (7)
