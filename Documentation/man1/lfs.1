.TH LFS 1 2024-08-29 Lu<PERSON>re "Lustre User Utilities"
.SH NAME
lfs \- client utility for Lustre-specific file layout and other attributes
.SH SYNOPSIS
.SY lfs
.SY lfs
.B --version
.SY lfs
.B --list-commands
.SY lfs
.B help
.YS
.SH DESCRIPTION
.B lfs
can be used to create a new file with a specific striping pattern, determine
the default striping pattern, gather the extended attributes (object numbers
and location) for a specific file. It can be invoked interactively without any
arguments or in a non-interactive mode with one of the arguments supported.
.SH COMMANDS
A number of sub-commands supported by lfs are listed below. Other commands
are described in separate man pages of the form
.BI lfs- COMMAND
as listed in the
.B SEE ALSO
section at the end.
.TP
.BR check " {" mgts | mdts | osts | all "} [" \fIPATH ]
Display the status of the MGTs, MDTs or OSTs (as specified in the command)
or all the servers (MGTs, MDTs and OSTs). If
.I PATH
is provided, display the status of the lustre file system mounted at specified
.I PATH
only. This command will make an RPC to all the servers in scope to discover
the server status. NOTE:
.B lfs osts
may be preferable for some use cases.
.TP
.BR data_version " [" -nrw "] " \fIFILENAME
Display the current version of file data and optionally set the data version
stored in the HSM xattr. If -n is specified, the data version is read without
taking a lock. As a consequence, the data version could be outdated if there are
dirty caches on filesystem clients, but this option will not force data flushes
and has less of an impact on the filesystem. If -r is specified, the data
version is read after dirty pages on clients are flushed. If -w is specified,
the data version is read after all caching pages on clients are flushed. If -s
is specified, the data version that was read, is also written to the HSM xattr.

Even with -r or -w, race conditions are possible and the data version should be
checked before and after an operation to be confident the data did not change
during it.
.IP
The data version is the sum of the last committed transaction numbers of all
data objects of a file. It is used by HSM policy engines for verifying that file
data has not been changed during an archive operation or before a release
operation, and by OST migration, primarily for verifying that file data has not
been changed during a data copy, when done in non-blocking mode.
.TP
.B flushctx
See lfs-flushctx(1).
.TP
.BR osts " [" PATH ]
List all the OSTs for all mounted filesystems. If a
.I PATH
is provided that is located on a lustre mounted file system
then only the OSTs belonging to that filesystem are displayed. This command will
use a cached value for the OST status and will not make a RPC to discover
status. The cached value will be updated by the client during the
course of normal operations and would typically be less than 30 seconds old.
NOTE:
.B lfs check osts
may be preferable for some use cases.
.TP
.BR pool_list " {" \fIFILESYSTEM }[ .\fIPOOLNAME "] | {" \fIPATHNAME }
List the pools in
.I FILESYSTEM
or
.IR PATHNAME ,
or the OSTs in
.IR FILESYSTEM.POOL .
.TP
.BI swap_layouts "FILENAME1 FILENAME2"
Swap the data (layout and OST objects) of two regular files. The
two files have to be in the same filesystem, owned by the same user,
reside on the same MDT and writable by the user.
.P
Swapping the layout of two directories is not permitted.
.TP
.B mkdir
lfs mkdir is documented in the man page: lfs-mkdir(1). NOTE:
.B lfs setdirstripe
is an alias of the command
.B lfs mkdir
.TP
.B mv
lfs mv is deprecated, use lfs
.B migrate
instead.
.TP
.B migrate
See lfs-migrate(1).
.TP
.B setstripe
See lfs-setstripe(1).
.TP
.B unlink_foreign
Remove the foreign files/dirs that are prevented to be using
regular unlink/rmdir commands/syscalls. Works also for regular files/dirs.
.TP
.B --version
Output the build version of the lfs utility.
Use "lctl lustre_build_version" to get the version of the Lustre kernel modules
.TP
.B --list-commands
Output a list of the commands supported by the lfs utility
.TP
.B help
Provides brief help on the various arguments
.TP
.B exit/quit
Quit the interactive lfs session
.SH NOTES
The usage of
.BR "lfs find" ,
.BR "lfs getstripe" ,
.BR "lfs hsm_*" ,
.BR "lfs setstripe" ,
.BR "lfs migrate" ,
.BR "lfs getdirstripe" ,
.BR "lfs setdirstripe" ,
.BR "lfs mkdir" ,
.BR "lfs flushctx" ,
.BR "lfs changelog" ,
.B "lfs changelog_clear"
and
.B "lfs project"
are explained in separate man pages.
.SH EXAMPLES
Check the status of all servers (MGT, MDT, OST):
.RS
.EX
.B # lfs check all
.EE
.RE
.PP
List all the OSTs:
.RS
.EX
.B # lfs osts
.EE
.RE
.PP
List all the MDTs:
.RS
.EX
.B # lfs mdts
.EE
.RE
.PP
Turn quotas of user and group off:
.RS
.EX
.B # lfs quotaoff -ug /mnt/lustre
.EE
.RE
.SH AVAILABILITY
The
.B lfs
command is part of the
.BR lustre (7)
filesystem package since release 0.10.0
.\" Added in commit 0.9.1
.SH SEE ALSO
.BR lfs-changelog (1),
.BR lfs-changelog_clear (1),
.BR lfs-df (1),
.BR lfs-fid2path (1),
.BR lfs-find (1),
.BR lfs-flushctx (1),
.BR lfs-getdirstripe (1),
.BR lfs-getname (1),
.BR lfs-getstripe (1),
.BR lfs-hsm (1),
.BR lfs_migrate (1),
.BR lfs-migrate (1),
.BR lfs-mkdir (1),
.BR lfs-path2fid (1),
.BR lfs-pcc (1),
.BR lfs-project (1),
.BR lfs-quota (1),
.BR lfs-setdirstripe (1),
.BR lfs-setquota (1),
.BR lfs-setstripe (1),
.BR lustre (7),
.BR lctl (8)
