.TH LFS-MIRROR-RESYNC 1 2025-05-23 <PERSON><PERSON><PERSON> "Lustre User Utilities"
.SH NAME
lfs-mirror-resync \- resynchronize an out-of-sync mirrored file
.SH SYNOPSIS
.SY "lfs mirror resync"
.RB [ --only
.IR MIRROR_ID [,...]]
.RB [ --bandwidth= | -W
.IR BANDWIDTH_MB ]
.RB [ --stats ]
.RB [ --stats-interval=\c
.IR INTERVAL_SECONDS ]
.IR MIRRORED_FILE " [" MIRRORED_FILE2 " ...]"
.YS
.SH DESCRIPTION
This command resynchronizes out-of-sync mirrored file(s) specified by the path
name
.IR MIRRORED_FILE .
.P
If there is no stale mirror for the
.IR MIRRORED_FILE (s),
then the command does nothing.
Otherwise, it will copy data from synced mirror to stale mirror(s),
and mark all successfully copied mirror(s) as SYNC.
.P
If
.B --only
option is not specified, the
.B nosync
mirror(s) (i.e.
components that have this flag set) will not be synced.
.P
If
.B --only
.IR MIRROR_ID [,...]
option is specified,
then the command will resynchronize the mirror(s) specified by the
IR MIRROR_ID (s),
even nosync mirror(s) will be resynchronized as well.
This option cannot be used when multiple mirrored files are specified.
.SH OPTIONS
.TP
.BR "--only \fIMIRROR_ID" [ , ...]
This option indicates which mirror(s) specified by
.IR MIRROR_ID (s)
needs to be resynchronized. The
.I MIRROR_ID
is the numerical unique identifier for a mirror. Multiple
.IR MIRROR_ID s
are separated by comma. This option cannot
be used when multiple mirrored files are specified.
.TP
.BR --stats
This option enables progress updates every 5 seconds, in YAML format.
.TP
.BI --stats-interval= STATS_INTERVAL
This option enables progress updates every
.I STATS_INTERVAL
seconds, in YAML format.
.TP
.BR -W ", " --bandwidth " \fIBANDWIDTH_MB\fR[" MG ]
Limit the read and write bandwidth a mirror resync consumes to no more than
.I BANDWIDTH_MB
MiB/s. An optional suffix can be used to specify units in
.BR M egabytes
or
.BR G igabytes
per second.
.SH EXAMPLES
Resynchronize all of the stale and sync-able mirror(s) for /mnt/lustre/file1
and /mnt/lustre/file2:
.RS
.EX
.B # lfs mirror resync /mnt/lustre/file1 /mnt/lustre/file2
.EE
.RE
.PP
Resynchronize mirrors with mirror ID 4 and 5 for /mnt/lustre/file1 even if they
are not marked as STALE:
.RS
.EX
.B # lfs mirror resync --only 4,5 /mnt/lustre/file1
.EE
.RE
.SH AVAILABILITY
The
.B lfs mirror resync
command is part of the
.BR lustre (7)
filesystem package since release 2.11.0
.\" Added in commit v2_10_55_0-57-g79da3738df
.SH SEE ALSO
.BR lfs (1),
.BR lfs-mirror-create (1),
.BR lfs-mirror-extend (1),
.BR lfs-mirror-split (1),
.BR lfs-mirror-verify (1),
.BR lfs-setstripe (1)
