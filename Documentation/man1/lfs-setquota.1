.TH LFS-SETQUOTA 1 2022-02-26 "Lustre" "Lustre Utilities"
.SH NAME
lfs-setquota \- set quota limits or grace time for users, groups or projects.
.SH SYNOPSIS
.BR "lfs setquota " { -u | --user | -g | --group | -p | --projid "} " \fIID
[\fB--pool \fIPOOLNAME\fR]
       [\fB-b|\fB--block-softlimit\fR \fIBLOCK_SOFTLIMIT\fR[\fBKMGTPE\fR]]
       [\fB-B|\fB--block-hardlimit\fR \fIBLOCK_HARDLIMIT\fR[\fBKMGTPE\fR]]
       [\fB-i|\fB--inode-softlimit\fR \fIINODE_SOFTLIMIT\fR[\fBKMGTPE\fR]]
       [\fB-I|\fB--inode-hardlimit\fR \fIINODE_HARDLIMIT\fR[\fBKMGTPE\fR]] \fIMOUNT_POINT
.TP
.BR "lfs setquota " { -u | --user | -g | --group | -p | --projid "} " \fIID
 {\fB--default\fR|\fB-D\fR|\fB--delete\fR} \fIMOUNT_POINT
.TP
.BR "lfs setquota " { -t | --times "} {" -h | -u | -g | -p "} [" "--pool " \fIPOOLNAME ]
 [\fB-b\fR|\fB--block-grace\fR \fIBLOCK_GRACE_TIME\fR]
 [\fB-i\fR|\fB--inode-grace\fR \fIINODE_GRACE_TIME\fR] \fIMOUNT_POINT
.TP
.BR "lfs setquota " { -U | --default-usr | -G | --default-grp | -P | --default-prj }
       [\fB-b\fR|\fB--block-softlimit\fR \fIBLOCK_SOFTLIMIT\fR[\fBKMGTPE\fR]]
       [\fB-B\fR|\fB--block-hardlimit\fR \fIBLOCK_HARDLIMIT\fR[\fBKMGTPE\fR]]
       [\fB-i\fR|\fB--inode-softlimit\fR \fIINODE_SOFTLIMIT\fR[\fBKMGTPE\fR]]
       [\fB-I\fR|\fB--inode-hardlimit\fR \fIINODE_HARDLIMIT\fR[\fBKMGTPE\fR]] \fIMOUNT_POINT
.TP
.BR "lfs setquota " { -u | --user | -g | --group | -p | --projid "} " \fIUID\fR|\fIGID\fR|\fIPROJID\fR
       [\fB--delete\fR] <\fIfilesystem\fR>
.BR "lfs setquota " { -u | --user | -g | --group | -p | --projid "} " \fIUID\fR|\fIGID\fR|\fIPROJID\fR
       [\fB-r\fR] <\fIfilesystem\fR>
.TP
.SH DESCRIPTION
.TP
.BR "lfs setquota " { -u | -g | -p }
Command sets the filesystem quotas for users, groups, or projects respectively.
Block limits unit is kilobyte (1024) by default, and block limits are always
kilobyte-granular (even if specified in bytes), block limits can be specified
with a
.BR K ", " M ", " G ", " T ", " P ", or " E
suffix to specify units of 2^10, 2^20, 2^30, 2^40, 2^50 and 2^60 bytes
respectively.
.TP
.BR -b | --block-softlimit " " \fIBLOCK_SOFTLIMIT
Specify block softlimit, zero means unlimited.
.TP
.BR -B | --block-hardlimit " " \fIBLOCK_HARDLIMIT
Specify block hardlimit, zero means unlimited. The block hardlimit should be
greater than the block softlimit if it is being specified.
.TP
.BR --delete
Delete the unused user, group, or project \fIID\fR limit.
.TP
.BR -D|--default
Set user/group/project to use the default quota limits.
.TP
.BR -g | --group " " \fIGROUPNAME | \fIGID
Set group quota for name \fIGROUPNAME\fR or numeric \fIGID\fR.
.TP
.BR -h | --help
Print usage message.
.TP
.BR -i | --inode-softlimit " " \fIINODE_SOFTLIMIT
Specify inode softlimit, zero means unlimited.
.TP
.BR -I | --inode-hardlimit " " \fIINODE_HARDLIMIT
Specify inode hardlimit, zero means unlimited. The inode hardlimit should be
greater than inode softlimit when it's being specified.
.TP
.BR -p | --projid " " \fIPROJID
Set project quota for numeric \fIPROJID\fR.
.TP
.BR --pool " " \fIPOOL_NAME
Set quota per OST pool \fIPOOL_NAME\fR.
.TP
.BR -r
Reset the internal quota data of the user|group|project quota. It can be used
to fix the quota containing corrupted internal data (such as, the quota grant).
.TP
.BR -u | --user " " \fIUSERNAME | \fIUID
Set user quota for \fIUNAME\fR or numeric \fIUID\fR.
.PP
.TP
.BR "lfs setquota " { -t | --times "} {" -h | -u | -g | -p "} " \fIGRACE_TIME
Command sets the filesystem quota grace time for users, groups, or projects.
The
.I GRACE_TIME
is a global value that can take two forms: an amount of time or the word
.BR notify .
Traditionally,
.I GRACE_TIME indicates the amount of time in seconds
that the soft quota limit can be exceeded before users are prevented from
creating new files or consuming more space.  This is intended to allow users
to exceed the quota limit for a short time to complete their work without
having to grant each user a larger hard quota limit.
.PP
The alternative value of
.B notify
informs users that are exceeding their soft quota limit (an '\fB*\fR' in the
quota output) that they
are nearing their hard quota limit, but does not block new writes until the hard
quota limit is reached.  The soft quota state is reset once the user, group,
or project reduces their space usage or file count below the soft quota limit.
Grace time is specified in "XXwXXdXXhXXmXXs" format or as an integer seconds
value.  The maximum is 2^48 - 1 seconds.
.TP
.BR -t | --times
Set quota grace times.
.TP
.BR -u | --user
Set grace time for user.
.TP
.BR -g | --group
Set grace time for group.
.TP
.BR -p | --projid
Set grace time for project.
.TP
.BR -b | --block-grace " {" notify | \fIBLOCK_GRACE_TIME }
Specify grace time for block quota in seconds.
.TP
.BR -i | --inode-grace " {" notify | \fIBLOCK_GRACE_TIME }
Specify grace time for inode quota in seconds.
.TP
.BR --pool " " \fIPOOL_NAME
Set grace time for user, group or project per OST pool \fIPOOL_NAME\fR.
.PP
.TP
.BR "lfs setquota " { -U | --default-usr | -G | --default-grp | -P | --default-prj }
Command sets the filesystem default limits for user, group, project quotas,
if set, users/groups/projects without specific quota setting will use
default quota limits automatically.
.TP
.BR -U | --default-usr
Set default user quota limit.
.TP
.BR -G | --default-grp
Set default group quota limit.
.T
.BR -P | --default-prj
Set default project quota limit.
.TP
.PP
.SH EXAMPLES
.TP
.B $ lfs setquota -u bob --block-softlimit 2G --block-hardlimit 1G /mnt/lustre
Set quotas of user `bob': 1GB block hardlimit and 2 GB block softlimit
.TP
.B $ lfs setquota -u bob --default /mnt/lustre
Set quotas of user `bob' to use default quota setting
.TP
.B $ lfs setquota -U --block-softlimit 1G --block-hardlimit 2G /mnt/lustre
Set system default user quota: 1 GB block softlimit and 2 GB block hardlimit
.TP
.B $ lfs setquota -t -u --block-grace 36000 --inode-grace 1w4d /mnt/lustre
Set grace times for user quotas: 36000 seconds for block quotas, 1 week and 4
days for inode quotas
.TP
.B $ lfs setquota -u ivan --pool flash_pool -B 1G /mnt/lustre
Set hard block limit 1G for user 'ivan' per pool 'flash_pool'
.TP
.B $ lfs setquota -t -u --block-grace 10000 --pool flash_pool /mnt/lustre
Set grace time 10000 seconds for block quotas per pool 'flash_pool'
.TP
.BR "lfs setquota " { -u | -g | -p "} " \fIUID\fR|\fIGID\fR|\fIPROJID\fR " " [\fB--delete\fR] " " <\fIfilesystem\fR>
Command deletes the unused user, group, or project \fIID\fR from quota settings.
.TP
.PP
.SH EXAMPLES
.TP
.B $ lfs setquota -u bob --delete /mnt/lustre
Delete unused user 'bob'.
.TP
.B $ lfs setquota -u bob -r /mnt/lustre
Reset the user 'bob'.
.SH SEE ALSO
.BR lfs (1),
.BR lfs-quota(1)
