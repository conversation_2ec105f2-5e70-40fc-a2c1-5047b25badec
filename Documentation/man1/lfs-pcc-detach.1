.TH LFS-PCC-DETACH 1 2019-04-15 "Lustre" "Lustre User Utilities"
.SH NAME
lfs-pcc-detach, lfs-pcc-detach_fid \- Detach given files from PCC
.SH SYNOPSIS
.SY "lfs pcc detach"
.RB [ --keep | -k ]
.IR FILE \ [...]
.SY "lfs pcc detach_fid"
.RB [ --keep | -k ]
.RB { --mnt | -m
.IR MNTPATH }
.IR FID \ [...]
.YS
.SH DESCRIPTION
Detach specified
.I FILE
or
.I FID
from the persistent client cache.
.SH OPTIONS
.TP
.BR -k ", " --keep
By default, the detach command will detach
.I FILE
from PCC permanently and remove the PCC copy after detach. This option
will only detach the file in memory, but keep the PCC copy in cache.
It allows the detached file to be attached
automatically at next open if the cached copy of the file is still valid.
.TP
.BR -m ", " --mnt
Specify the Lustre client mountpoint for the file
.I FID
to be detached.
.SH EXAMPLES
Detach the file permanently from PCC. The cached file on PCC will be removed
after detach. IO to the file will come to Lustre OSTs after this command:
.EX
.RS
.B $ lfs pcc detach /mnt/lustre/test
.RE
.EE
.PP
Detach the file referenced by FID "0x200005348:0x101:0x0" from PCC
permanently, and the cached file on PCC will be removed after detach:
.EX
.RS
.B $ lfs pcc detach_fid -m /mnt/lustre 0x200005348:0x101:0x0
.RE
.EE
.PP
Detach the file "/mnt/lustre/test" from PCC. The client will try to attach
this file again at the next open if the cached copy is still valid:
.EX
.RS
.B $ lfs pcc detach -k /mnt/lustre/test
.RE
.EE
.PP
Detach the file referenced by FID "0x200000401:0x1:0x0" from PCC. The client
will try to attach this file again at the next open if the cached copy is still
valid:
.EX
.RS
.B $ lfs pcc detach_fid -k -m /mnt/lustre 0x200000401:0x1:0x0
.RE
.EE
.SH AVAILABILITY
.B lfs pcc detach
is part of the
.BR lustre (7)
filesystem package since release 2.13.0
.\" Added in commit v2_12_53-113-gf172b11688
.SH SEE ALSO
.BR lfs (1),
.BR lfs-pcc (1),
.BR lfs-pcc-attach (1),
.BR llapi_pcc_detach (3),
.BR lctl-pcc (8)
