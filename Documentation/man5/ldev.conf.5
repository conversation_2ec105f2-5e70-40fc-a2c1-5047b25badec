.TH LDEV.CONF 5 2024-08-29 Lu<PERSON>re "Lustre File Formats"
.SH NAME
/etc/ldev.conf \- lustre device configuration file
.SH DESCRIPTION
The ldev.conf file contains a list of Lustre devices used by the
.B ldev
utility.
.SH FORMAT
Comments beginning with a hash (#) are ignored. Each line represents one
device and includes the following information separated by a white space:
.TP
.I lOCAL_HOSTNAME
The name of the host where the device normally runs.
.TP
.I FOREIGN_HOSTNAME
The name of the host where the device runs when failed over.
If failover is not used, insert a hyphen as a placeholder.
.TP
.I LABEL
The Lustre label associated with the device in the form
.IB FSNAME -\c
.RB { OST|MDT }\c
.I NNNN
where
.I FSNAME
is the file system name,
followed by either
.B OST
or
.BR MDT ,
and
.I NNNN
is the four-digit hex index of the device.
.TP
.I DEVICE_PATH
The path name of the device. In failover configurations it should be available
on both local and foreign hosts, e.g. use the symlinks maintained by udev
in
.IR /dev/disk/by-id .
.TP
.I JOURNAL_PATH
The path name of the journal device, if any. This field may be omitted unless
the raidtab field is present. If a journal device is not used a hyphen may be
inserted as a placeholder.
.TP
.I RAIDTAB
The path name of a Linux software raid configuration file or ZFS cache file.
Using non-default names for these files may help prevent arrays from being
automatically started by the system. This is important in failover
configurations where the timing of device initialization must be strictly
controlled. This field may be omitted.
.SH EXAMPLES
.EX
\&# ldiskfs on block device example
.BI # " LOCAL FOREIGN" /- " LABEL " [md:|zfs:] "DEVICE_PATH " \
[ JOURNAL_PATH "]/- [" RAIDTAB ]
tycho-mds1 -    lc1-MDT0000 /dev/sda                /dev/sdc
tycho1  tycho5  lc1-OST0000 /dev/disk/by-id/scsi-10103a262891d340100
tycho1  tycho5  lc1-OST0008 /dev/disk/by-id/scsi-10103a262681d340200
tycho1  tycho5  lc1-OST0010 /dev/disk/by-id/scsi-10103a2629e1d340300
tycho5  tycho1  lc1-OST0004 /dev/disk/by-id/scsi-101046e6b401d341100
tycho5  tycho1  lc1-OST000c /dev/disk/by-id/scsi-101046e6b591d341200
tycho5  tycho1  lc1-OST0014 /dev/disk/by-id/scsi-101046e6bb41d341300
\&
\&# ldiskfs on Linux software RAID example
.BI # " LOCAL FOREIGN" /- " LABEL " [md:|zfs:] "DEVICE_PATH " \
[ JOURNAL_PATH "]/- [" RAIDTAB ]
zwicky-mds1  -    zwicky-MDT0000 md:/dev/md0 -         /etc/mdadm.conf.mds
zwicky1  zwicky2  zwicky-OST0000 md:/dev/md0 /dev/md10 /etc/mdadm.conf.oss
zwicky2  zwicky1  zwicky-OST0001 md:/dev/md1 /dev/md20 /etc/mdadm.conf.oss
\&
\&# ZFS example
.BI # " LOCAL FOREIGN" /- " LABEL " [md:|zfs:] "DEVICE_PATH " \
[ JOURNAL_PATH "]/- [" RAIDTAB ]
zeno-mds1 -   zeno-MDT0000 zfs:lustre-zeno-mds1/mdt1 - /etc/zfs/zpool.cache.zeno
zeno1  zeno5  zeno-OST0000 zfs:lustre-zeno1/ost1     - /etc/zfs/zpool.cache.zeno
zeno5  zeno1  zeno-OST0001 zfs:lustre-zeno5/ost1     - /etc/zfs/zpool.cache.zeno
.EE
.SH AVAILABILITY
.B /etc/ldev.conf
is part of the
.BR lustre (7)
filesystem package since release 2.3.0
.\" Added in commit 2.2.90-9-g04a38ba7cd
.SH SEE ALSO
.BR ldev (8)
