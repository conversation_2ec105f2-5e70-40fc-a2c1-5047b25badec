.TH NIDS 5 2024-08-28 Lu<PERSON>re "Lustre File Formats"
.SH NAME
/etc/nids \- The static lookup table for Lustre NIDs
.SH SYNOPSIS
The static lookup table for Lustre NIDs
.SH DESCRIPTION
The nids file maps host names to NIDs and vice-versa.
.SH FORMAT
Comments beginning with a hash (#) are ignored. Each line represents one
host and includes the following information separated by white space:
.TP
.I HOSTNAME
The primary hostname of the node, e.g.
.IR "uname -n" .
.TP
.I PRIMARY_NID
The primary NID of the node.
.TP
.IR OTHER_NID " ..."
Any additional NIDs.
.SH FILES
/etc/nids
.SH EXAMPLES
.EX
\&## Tycho
tycho-mds1 ************@tcp *************@tcp
tycho1     **********@tcp   ***********@tcp
tycho2     **********@tcp   ***********@tcp
tycho3     **********@tcp   ***********@tcp
tycho4     **********@tcp   ***********@tcp
tycho5     **********@tcp   ***********@tcp
tycho6     **********@tcp   ***********@tcp
tycho7     **********@tcp   ***********@tcp
tycho8     **********@tcp   ***********@tcp
.EE
.SH AVAILABILITY
.B /etc/nids
is part of the
.BR lustre (7)
filesystem package since release 2.3.0
.\" Added in commit 2.2.90-9-g04a38ba7cd
.SH SEE ALSO
