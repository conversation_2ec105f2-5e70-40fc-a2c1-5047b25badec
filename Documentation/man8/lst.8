.\" SPDX-License-Identifier: GPL-2.0
.
.TH lst 8 "2017 Jan 12" Lustre "configuration utilities"
.SH NAME
lst \- Start the Lustre LNET Self-test
.SH SYNOPSIS
.B "lst"
.br
.B "lst --list-commands"
.SH DESCRIPTION
LNET self-test helps site administrators confirm that Lustre
Networking (LNET) has been properly installed and configured.
The self-test also confirms that LNET and the network software
and hardware underlying it are performing according to expectations.
.LP
Every LNET self-test runs in the context of a session.  A node can be
associated with only one session at a time to ensure the session has
exclusive use of the nodes on which it is running. A session is created,
controlled and monitored from a single node; this is referred to as the
self-test console.
.LP
Any node may act as the self-test console.  Nodes are named and allocated
to a self-test session in groups.  This allows all nodes in a group to
be referenced by a single name.
.LP
Test configurations are built by describing and running test batches.
A test batch is simply a named collection of tests, with each test
composed of a number of individual point-to-point tests running in parallel.
These individual point-to-point tests are instantiated according to the
test type, source group, target group and distribution specified when the
test is added to the test batch.
.LP
.SH OPTIONS
.TP
.B --list-commands
Output a list of the commands supported by the lst utility
.SH MODULES
To run LNET self-test, load these modules: libcfs, lnet, lnet_selftest
and any one of the klnds (i.e, ksocklnd, ko2iblnd...).
To load all necessary modules, run modprobe lnet_selftest, which
recursively loads the modules that lnet_selftest depends on.
.LP
There are two types of nodes for LNET self-test: the console node and
test nodes. Both node types require all previously-specified modules
to be loaded. (The userspace test node does not require these modules.)
.LP
A console user can invite a test node to join the test session by running
lst add_group \fINID\fR.
.LP
.SH UTILITIES
.B lst
is the user interface for the self-test console (run on console node).
It provides a list of commands to control the entire test system,
such as create session, create test groups, etc.
.LP
.SH EXAMPLE SCRIPT
Below is a sample LNET self-test script which simulates the traffic
pattern of a set of Lustre servers on a TCP network, accessed by Lustre
clients on an IB network (connected via LNET routers), with half the
clients reading and half the clients writing.
.LP
.nf
#!/bin/bash
export LST_SESSION=$$
lst new_session read/write
lst add_group servers 192.168.10.[8,10,12-16]@tcp
lst add_group readers 192.168.1.[1-253/2]@o2ib
lst add_group writers 192.168.1.[2-254/2]@o2ib
lst add_batch bulk_rw
lst add_test --batch bulk_rw --from readers --to servers \
    brw read check=simple size=1M
lst add_test --batch bulk_rw --from writers --to servers \
    brw write check=full size=4K
# start running
lst run bulk_rw
# display server stats for 30 seconds
lst stat servers & sleep 30; kill $!
# tear down
lst end_session
.fi
.SH SEE ALSO
This manual page was extracted from Introduction to LNET Self-Test,
section 19.4.1 of the Lustre Operations Manual.  For more detailed
information, please refer to that document.
