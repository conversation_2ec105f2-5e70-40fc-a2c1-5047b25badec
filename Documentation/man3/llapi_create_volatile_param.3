.TH LLAPI_CREATE_VOLATILE_PARAM 3 2024-08-23 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_create_volatile_param \- Lustre API file management
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_create_volatile_param(const char *" directory ", int " mdt_indx ,
.BI "                                int " open_flags ", mode_t " mode ,
.BI "                                const struct llapi_stripe_param *" stripe_param )
.PP
.BI "int llapi_create_volatile_idx(char *" directory ", int " idx ", int " open_flags )
.PP
.BI "int llapi_create_volatile(char *" directory ", int " mode )
.fi
.SH DESCRIPTION
These three functions create an anonymous, temporary, volatile file on
a Lustre filesystem. The created file is not visible with
.BR ls (1).
Once the file is closed, or the owning process dies,
the file is permanently removed from the filesystem.
.P
These functions will also work on a non\-Lustre filesystem, where the
file is created then unlinked, leaving only the file descriptor to
access the file. This is not strictly equivalent because there is a
small window during which the file is visible to users (provided they
have access to the
.IR directory ).
.P
The
.I directory
parameter indicates where to create the file on the Lustre filesystem.
.P
.I mdt_idx
is the MDT index onto which to create the file.
To use a default MDT, set mdt_idx to \-1.
.P
.I open_flags
and
.I mode
are the same as
.BR open (2).
.P
.I stripe_param
describes the striping information.
If it is NULL, then the default for the directory is used.
.SH RETURN VALUE
.BR llapi_create_volatile_param ,
.B llapi_create_volatile_idx
and
.B llapi_create_volatile
return a file descriptor on success.
They all return a negative errno on failure.
.SH ERRORS
The negative errno can be, but is not limited to:
.TP 15
.B -EINVAL
An invalid value was passed.
.TP 15
.B -ENOMEM
Not enough memory to allocate a resource.
.SH AUTHORS
Frank Zago for Cray Inc.
.SH AVAILABILITY
.BR llapi_create_volatile_param() ,
.B llapi_create_volatile_idx()
and
.B llapi_create_volatile()
are part of the
.BR lustre (7)
user application interface library since release 2.4.0
.\" Added in commit 2.3.53-7-gf715e4e298
.SH SEE ALSO
.BR lustreapi (7)
