.TH LLAPI_CHANGELOG_GET_FD 3 "2024-08-22 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_changelog_get_fd \- Return the file descriptor of the changelog device
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_changelog_get_fd(void *" priv ");"
.fi
.SH DESCRIPTION
The function
.B llapi_changelog_get_fd()
returns the file descriptor of changelog char device for the instance
.IR priv .
.PP
This can be used to call
.B poll()
on the file descriptor to get events on the
changelog device.
.SH RETURN VALUES
Return the file descriptor on success or a negative errno value on failure.
.SH ERRORS
.TP 15
.SM -EINVAL
.I priv
argument is invalid.
.SH AVAILABILITY
.B llapi_changelog_get_fd
is part of the
.BR lustre (7)
user application interface library since release 2.4.0
.\# Added in commit 2.3.53-7-gf715e4e298
.SH SEE ALSO
.BR poll (2),
.<PERSON> llapi_changelog_clear (3),
.<PERSON> llapi_changelog_in_buf (3),
.BR llapi_changelog_recv (3),
.BR llapi_changelog_start (3),
.BR lustreapi (7)
