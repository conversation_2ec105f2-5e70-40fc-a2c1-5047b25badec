.TH LLAPI_HSM_ACTION_BEGIN 3 2024-08-27 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_hsm_action_begin, llapi_hsm_action_end, llapi_hsm_action_progress, llapi_hsm_action_get_dfid, llapi_hsm_action_get_fd \- Lustre API copytool management
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_hsm_action_begin(struct hsm_copyaction_private **" phcp ,
.BI "                           const struct hsm_copytool_private *" ct ,
.BI "                           const struct hsm_action_item *" hai ,
.BI "                           int " restore_mdt_index ,
.BI "                           int " restore_open_flags ", bool " is_error );
.PP
.BI "int llapi_hsm_action_end(struct hsm_copyaction_private **" phcp ,
.BI "                         const struct hsm_extent *" he ", int " hp_flags ,
.BI "                         int " errval );
.PP
.BI "int llapi_hsm_action_progress(struct hsm_copyaction_private *" hcp ,
.BI "                              const struct hsm_extent *" he ,
.BI "                              __u64 " total ", int " hp_flags );
.PP
.BI "int llapi_hsm_action_get_dfid(const struct hsm_copyaction_private *" hcp ,
.BI "                              lustre_fid  *" fid );
.PP
.BI "int llapi_hsm_action_get_fd(const struct hsm_copyaction_private *" hcp );
.fi
.SH DESCRIPTION
When a copytool is ready to process an HSM action received through
.BR llapi_hsm_copytool_recv() ,
it must first call
.B llapi_hsm_action_begin()
to initialize the internal action state, stored in
.IR phcp .
.I ct
is the opaque copytools handle
previously returned by
.BR llapi_hsm_copytool_register() .
.I hai
is the request.
.I restore_mdt_index
and
.I restore_open_flags
are only used for an
.B HSMA_RESTORE
type of request.
.I restore_mdt_index
is the MDT index on which to create the restored file, or \-1 for default.
If the copytool does not intend to process the request, it should set
.I is_error
to
.BR true ,
and then call
.BR llapi_hsm_action_end() .
.P
While performing a copy (i.e. the HSM request is either
.B HSMA_ARCHIVE
or
.BR HSMA_RESTORE ),
the copytool can inform Lustre of the progress of the operation with
.BR llapi_hsm_action_progress() .
.I he
is the interval
.RI ( offset ", " length )
of the data copied.
Each interval must be unique; i.e. there must not be any overlap.
.I length
is the total length that is expected to be transfered.
.I hp_flags
should be 0. The progress can be checked on any Lustre client by
calling
.BR llapi_hsm_current_action() , or by using
.BR lfs hsm_action .
.P
Once the HSM request has been performed, the destination file must be
closed, and
.B llapi_hsm_action_end()
must be called to free\-up the
allocated resources and signal Lustre that the file is now available
to consumers.
.I errval
is set to 0 on success. On error, it must be an errno,
and hp_flags can be set to
.B HP_FLAG_RETRY
if the request is retryable, 0 otherwise.
.I he
is the interval
.IR ( offset ", " length )
of the data copied. It can be the
.I hai_extent
of the HSM request.
.P
For a restore operation, a volatile file, invisible to ls, is
created.
.B llapi_hsm_action_get_fd()
will return a file descriptor to it.
It is the responsibility of the copytool to close the returned
file descriptor when the data transfer is done.
.B llapi_hsm_action_get_dfid()
will return the FID of the volatile file, which can then be used with
.B llapi_open_by_fid()
to open the file in a different process, or on a different node.
.P
.B llapi_hsm_action_get_fd()
and
.B llapi_hsm_action_get_dfid()
can be called for an archive operation too. The returned file
descriptor and the FID are from the file to be archived.
.SH RETURN VALUE
.B llapi_hsm_action_get_fd()
returns a file descriptor on success. The other functions return 0 on success.
All functions return a negative errno on failure.
.SH ERRORS
The negative errno can be, but is not limited to:
.TP 15
.B -EINVAL
An invalid value was passed, the copytool is not registered, ...
.TP
.B -ENOMEM
Not enough memory to allocate a resource.
.SH AUTHORS
Frank Zago
.SH AVAILABILITY
.BR llapi_hsm_action_begin(),
.BR llapi_hsm_action_end(),
.BR llapi_hsm_action_progress(),
.B llapi_hsm_action_get_dfid()
and
.B llapi_hsm_action_get_fd()
are part of the
.BR lustre (7)
user application interface library since release 2.4.0
.\" Added in commit 2.3.53-7-gf715e4e298
.SH SEE ALSO
.BR lfs (1),
.BR llapi_hsm_copytool_recv (3),
.BR llapi_hsm_copytool_register (3),
.BR lustreapi (7)
.P
See
.I lhsmtool_posix.c
in the Lustre sources for a use case of this API.
