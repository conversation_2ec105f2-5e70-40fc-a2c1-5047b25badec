.TH LLAPI_IOCTL 3 2024-08-27 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_ioctl \- call ioctl() with possible command remapping
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_ioctl(int " fd ", unsigned int " cmd ", void *" buf ");"
.fi
.SH DESCRIPTION
.B llapi_ioctl()
calls the
.BR ioctl (2)
system call with the supplied arguments.  However, if the ioctl() returns
.B ENOTTY
(no such ioctl) then if
.I cmd
has been remapped from an older ioctl command number the ioctl() call will
be retried with the old ioctl command number.
.SH RETURN VALUES
.B llapi_ioctl()
typically returns:
.TP
0
on success in most cases
.TP
<0
a negative value on failure and sets
.BR errno .
Due to the variety of ioctl commands, it may return different values
and errno as appropriate for the
.IR fd ,
.I cmd
number, and argument
.I buf
passed.
.SH EXAMPLES
.nf
#include <lustre/lustreapi.h>
\&
int main(int argc, char *argv[])
{
        unsigned long group = 1234;
        int rc, fd;
\&
        fd = open(argv[1], O_RDONLY);
        if (rc < 0) {
                fprintf(stderr, "error opening file '%s': %s\\n",
                        argv[1], strerror(errno));
                return -1;
        }
\&
        rc = llapi_ioctl(fd, LL_IOC_GROUP_LOCK, &group);
        if (rc < 0) {
                fprintf(stderr, "%s: ioctl error: %s\\n",
                        argv[1], strerror(errno));
                return rc;
        }
\&
        printf("%s: locked with group %lu\\n", argv[1], group);
        return 0;
}
.fi
.SH AVAILABILITY
.B llapi_ioctl()
is part of the
.BR lustre (7)
user application interface library since release 2.16.0
.\" Added in commit v2_15_55-173-g594b780062
.SH SEE ALSO
.BR ioctl (2),
.BR lustre (7),
.BR lustreapi (7)
