.TH LLAPI_PROJECT_CLOSE 3 2025-07-01 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_project_close \- close project mapping file
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.sp
.BI "int llapi_project_close(struct ll_project_handle *" hdl );
.fi
.SH DESCRIPTION
The
.B llapi_project_close()
function closes the file specified by
.BR hdl->lph_file ,
and releases the state in
.BR hdl .
.SH RETURN VALUES
.TP
.B 0
on success
.TP
-ve
error code on failure and sets errno appropriately.
.SH ERRORS
.TP 15
.B -EINVAL
if either
.I hdl
is NULL, or
.I hdl->lph_magic
is invalid.
.TP
.B -errno
if
.I hdl->lph_file
fails to close.
.SH AVAILABILITY
.B llapi_project_close()
is part of the
.BR lustre (7)
user application interface library since release 2.17.0
.\" Added in commit v2_16_55-17-g75c6d5636d
.SH SEE ALSO
.BR lfs_project (1),
.<PERSON> llapi_project_fgetnam (3),
.<PERSON> llapi_project_fgetprjid (3),
.<PERSON> llapi_project_get (3),
.BR llapi_project_getnam (3),
.BR llapi_project_getprjid (3),
.BR llapi_project_open (3),
.BR llapi_project_put (3),
.BR projid (5),
.BR lustreapi (7)
