.TH LLAPI_LAYOUT_COMP_USE_ID 3 2024-08-27 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_layout_comp_use_id \- set currently active component to given ID
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_layout_comp_use_id(struct llapi_layout *" layout ",
.BI "                             uint32_t *" comp_id );
.fi
.SH DESCRIPTION
Sets currently active component of
.I layout
to the component with specified
.IR comp_id .
.SH RETURN VALUES
Return 0 on success. Otherwise, if an error occurred -1 is returned and
.I errno
is set appropriately.
.SH ERRORS
.TP 15
.B ENOENT
No such component ID exists.
.TP
.B EINVAL
An invalid argument was specified.
.SH AVAILABILITY
.B llapi_layout_comp_use_id()
is part of the
.BR lustre (7)
user application interface library since release 2.10.0
.\" Added in commit v2_9_55_0-18-gc4702b7443
.SH SEE ALSO
.BR llapi_layout_alloc (3),
.BR llapi_layout_comp_del (3),
.BR llapi_layout_comp_id_get (3),
.BR llapi_layout_comp_use (3),
.BR llapi_layout_file_open (3),
.BR llapi_layout (7),
.BR lustreapi (7)
