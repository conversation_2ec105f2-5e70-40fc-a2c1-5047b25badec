.TH LLAPI_QUOTACTL 3 2024-08-28 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_quotactl \- manipulate disk quotas on a Lustre filesystem
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_quotactl(char *" mnt ", struct if_quotactl *" qctl );
.PP
.B struct if_quotactl {
.RS 8
.B "__u32                   qc_cmd;"
.B "__u32                   qc_type;"
.B "__u32                   qc_id;"
.B "__u32                   qc_stat;"
.B "struct obd_dqinfo       qc_dqinfo;"
.B "struct obd_dqblk        qc_dqblk;"
.B "char                    obd_type[16];"
.B "struct obd_uuid         obd_uuid;"
.RE
.B };
.PP
.B struct obd_dqblk {
.RS 8
.B __u64 dqb_bhardlimit;
.B __u64 dqb_bsoftlimit;
.B __u64 dqb_curspace;
.B __u64 dqb_ihardlimit;
.B __u64 dqb_isoftlimit;
.B __u64 dqb_curinodes;
.B __u64 dqb_btime;
.B __u64 dqb_itime;
.B __u32 dqb_valid;
.B __u32 padding;
.RE
.B };
.PP
.B struct obd_dqinfo {
.RS 8
.B __u64 dqi_bgrace;
.B __u64 dqi_igrace;
.B __u32 dqi_flags;
.B __u32 dqi_valid;
.RE
.B };
.sp
.B struct obd_uuid {
.RS 8
.B char uuid[40];
.RE
.B };
.fi
.SH DESCRIPTION
The
.B llapi_quotactl(\|)
function manipulates disk quotas on a Lustre filesystem
.IR mnt .
.I qc_cmd
indicates a command to be applied to 
.B UID
.I qc_id
or 
.B GID
.IR qc_id .
.TP 15
.B LUSTRE_Q_QUOTAON
Turn on quotas for a Lustre filesystem. Deprecated as of 2.4.0.
.I qc_type
is USRQUOTA, GRPQUOTA or UGQUOTA (both user and group quotas).
The quota files must exist; they are normally created with the
.BR llapi_quotacheck (3)
call.
.PP
This call is restricted to the super-user.
.TP
.B LUSTRE_Q_QUOTAOFF
Turn off quotas for a Lustre filesystem. Deprecated as of 2.4.0.
.I qc_type
is USRQUOTA, GRPQUOTA or UGQUOTA (both user and group quotas).
.PP
This call is restricted to the super-user.
.TP
.B LUSTRE_Q_GETQUOTA
Get disk quota limits and current usage for user or group
.IR qc_id .
.I qc_type
is USRQUOTA or GRPQUOTA.
.I uuid
may be filled with OBD UUID string
to query quota information from a specific node.
.I dqb_valid
may be set nonzero to query information only from MDS. If
.I uuid
is an empty string and
.I dqb_valid
is zero then clusterwide limits and usage are returned. On return
.I obd_dqblk
contains the requested information (block limits unit is kilobyte).
.I dqb_btime
and
.I dqb_itime
are block and inode softlimit grace period expiration
timestamps for the requested user or group.
.PP
Quotas must be turned on before using this command.
.TP
.B LUSTRE_Q_SETQUOTA
Set disk quota limits for user or group
.IR qc_id .
.I qc_type
is USRQUOTA or GRPQUOTA.
.I dqb_valid
must be set to QIF_ILIMITS, QIF_BLIMITS or QIF_LIMITS
(both inode limits and block limits) dependent on updating limits.
.I obd_dqblk
must be filled with limits values (as set in
.IR dqb_valid ,
block limits unit is kilobyte).
.PP
Quotas must be turned on before using this command.
.TP
.B LUSTRE_Q_GETINFO
Get information about quotas.
.I qc_type
is either USRQUOTA or GRPQUOTA. On return
.I dqi_igrace
is the default inode grace period duration
for all users or all groups (in seconds),
.I dqi_bgrace
is the default block grace period duration
for all users or all groups (in seconds),
.I dqi_flags
is not used by the current Lustre version.
.TP
.B LUSTRE_Q_SETINFO
Set quota information (like grace times).
.I qc_type
is either USRQUOTA or GRPQUOTA.
.I dqi_igrace
is inode grace time (in seconds),
.I dqi_bgrace
is block grace time (in seconds),
.I dqi_flags
is not used by the current Lustre version and must be zeroed.
.SH RETURN VALUES
.B llapi_quotactl(\|)
returns:
.TP
0
on success.
.TP
\-1
on failure and sets
.B errno
to indicate the error.
.SH ERRORS
.TP 15
.SM EFAULT
.I qctl
is invalid.
.TP
.B ENOSYS
The kernel or Lustre modules have not been compiled with the
.B QUOTA
option.
.TP
.B ENOMEM
Insufficient memory to complete operation.
.TP
.B ENOTTY
.I qc_cmd
is invalid.
.TP
.B EBUSY
Cannot process during quotacheck.
.TP
.B ENOENT
.I uuid
does not correspond to OBD or
.I mnt
does not exist
.TP
.B EPERM
The call is privileged and the caller was not the super-user.
.TP
.B ESRCH
No disk quota is found for the indicated user.
.IP
Quotas have not been turned on for this filesystem.
.SH AVAILABILITY
.B llapi_quotactl()
is part of the
.BR lustre (7)
user application interface library since release 2.4.0
.\" Added in commit 2.3.53-7-gf715e4e298
.SH SEE ALSO
.BR lfs (1),
.BR lustre (7),
.BR lustreapi (7)
