.TH LLAPI_PATH2FID 3 2024-08-28 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_path2fid \- translate a path name to a File Identifier
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_path2fid(const char *" path ", struct lu_fid *" fid );
.fi
.SH DESCRIPTION
.BR llapi_path2fid()
stores the Lustre file identifier (FID) for the file or directory named by
.I path
into
.IR fid .
The
.I fid
may be then be passed to other
.B llapi_
functions that expect the
.B struct lu_fid
data type.
.SH RETURN VALUES
returns 0 on success or a negative errno value on failure.
.SH ERRORS
.TP 15
.B -EINVAL
An invalid argument was specified.
.TP
.B -ENOTTY
.I path
does not reside on a Lustre filesystem.
.TP
.B -ENOENT
.I path
does not exist.
.SH AVAILABILITY
.B llapi_path2fid()
is part of the
.BR lustre (7)
user application interface library since release 2.4.0
.\" Added in commit 2.3.53-7-gf715e4e298
.SH SEE ALSO
.BR llapi_fid2path (3),
.BR llapi_layout_get_by_fid (3),
.BR llapi_path2parent (3),
.BR llapi_layout (7),
.BR lustreapi (7)
