.TH LLAPI_FILE_GET_STRIPE 3 2024-08-23 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_file_get_stripe \- get striping information for a file or a directory on a Lustre filesystem
.SH SYNOPSIS
.nf
.B #include <sys/types.h>
.B #include <sys/stat.h>
.B #include <fcntl.h>
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_file_get_stripe(const char *"path ", void *"lum );
.fi
.SH DESCRIPTION
.LP
.B llapi_file_get_stripe()
returns striping information for a file or a directory 
.I path
in
.I lum
(which should point to a large enough memory region) in one of the following formats:
.PP
.nf
struct lov_user_md_v1 {
        __u32 lmm_magic;
        __u32 lmm_pattern;
        __u64 lmm_object_id;
        __u64 lmm_object_seq;
        __u32 lmm_stripe_size;
        __u16 lmm_stripe_count;
        __u16 lmm_stripe_offset;
        struct lov_user_ost_data_v1 lmm_objects[];
} __attribute__((packed));
\&
struct lov_user_md_v3 {
        __u32 lmm_magic;
        __u32 lmm_pattern;
        __u64 lmm_object_id;
        __u64 lmm_object_seq;
        __u32 lmm_stripe_size;
        __u16 lmm_stripe_count;
        __u16 lmm_stripe_offset;
        char  lmm_pool_name[LOV_MAXPOOLNAME + 1];
        struct lov_user_ost_data_v1 lmm_objects[];
} __attribute__((packed));
.fi
.TP 20
.I lmm_magic
specifies the format of the returned striping information.
.BR LOV_MAGIC_V1
is used for lov_user_md_v1.
.BR LOV_MAGIC_V3
is used for lov_user_md_v3.
.TP 20
.I lmm_pattern
holds the striping pattern. Only
.BR LOV_PATTERN_RAID0
is possible in this Lustre version.
.TP 20
.I lmm_object_id
holds the MDS object id.
.TP 20
.I lmm_object_gr
holds the MDS object group.
.TP 20
.I lmm_stripe_size
holds the stripe size in bytes.
.TP 20
.I lmm_stripe_count
holds the number of OSTs the file is striped across.
.TP 20
.I lmm_stripe_offset
holds the OST index from which the file starts.
.TP 20
.I lmm_pool_name
holds the OST pool name to which the file belongs.
.TP 20
.I lmm_objects
is an array of
.I lmm_stripe_count
members containing per OST file information in the following format:
.PP
.nf
struct lov_user_ost_data_v1 {
        __u64 l_object_id;
        __u64 l_object_seq;
        __u32 l_ost_gen;
        __u32 l_ost_idx;
} __attribute__((packed));
.fi
.TP 20
.I l_object_id
holds the OST object id.
.TP 20
.I l_object_seq
holds the OST object group.
.TP 20
.I l_ost_gen
holds the generation of the OST index.
.TP 20
.I l_ost_idx
holds the OST index in LOV.
.SH RETURN VALUES
.LP
.B llapi_file_get_stripe()
returns:
.TP
0
on success
.TP
!= 0
on failure,
.I errno
is set appropriately.
.SH ERRORS
.TP 15
.SM ENOMEM
failed to allocate memory.
.TP 15
.SM ENAMETOOLONG
.I path
was too long.
.TP 15
.SM ENOENT
.I path
does not point to a file or a directory.
.TP 15
.SM ENOTTY
.I path
does not point to a Lustre filesystem.
.TP 15
.SM EFAULT
memory region pointed by
.I lum
is not properly mapped.
.SH EXAMPLES
.nf
#include <sys/vfs.h>
#include <lustre/lustreapi.h>
\&
static inline int maxint(int a, int b)
{
        return a > b ? a : b;
}
\&
static void *alloc_lum()
{
        int v1, v3, join;
\&
        v1 = sizeof(struct lov_user_md_v1) +
             LOV_MAX_STRIPE_COUNT * sizeof(struct lov_user_ost_data_v1);
        v3 = sizeof(struct lov_user_md_v3) +
             LOV_MAX_STRIPE_COUNT * sizeof(struct lov_user_ost_data_v1);
\&
        return malloc(maxint(v1, v3));
}
\&
int main(int argc, char** argv)
{
        struct lov_user_md *lum_file = NULL;
        int rc;
        int lum_size;
\&
        if (argc != 2) {
                fprintf(stderr, "Usage: %s <filename>\\n", argv[0]);
                return 1;
        }
\&
        lum_file = alloc_lum();
        if (lum_file == NULL) {
                rc = ENOMEM;
                goto cleanup;
        }
\&
        rc = llapi_file_get_stripe(argv[1], lum_file);
        if (rc) {
                rc = errno;
                goto cleanup;
        }
\&
        /* stripe_size stripe_count */
        printf("%d %d\\n",
               lum_file->lmm_stripe_size,
               lum_file->lmm_stripe_count);
\&
cleanup:
        if (lum_file != NULL)
                free(lum_file);
\&
        return rc;
}
.fi
.SH AVAILABILITY
.B llapi_file_get_stripe()
is part of the
.BR lustre (7)
user application interface library since release 2.4.0
.\" Added in commit 2.3.53-7-gf715e4e298
.SH SEE ALSO
.BR lustre (7),
.BR lustreapi (7)
