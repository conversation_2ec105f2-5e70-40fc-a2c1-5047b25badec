.TH LLAPI_FID_PARSE 3 2024-08-23 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_fid_parse \- parse ASCII FID string into binary lu_fid
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_fid_parse(const char *" fidstr ", struct lu_fid *" fid ,
.BI "                    char **" endptr ");"
.PP
.fi
.SH DESCRIPTION
.B llapi_fid_parse()
converts an ASCII FID string into a binary
.B struct lu_fid
for use in other interfaces such as
.BR llapi_layout_get_by_fid() .
.I fidstr
should contain three numbers in the form
.IR fid_seq : fid_oid : fid_ver
and may optionally be enclosed in square braces
.BR [] .
It will skip any leading whitespace before the FID.
.LP
If
.I endptr
is not NULL,
.B llapi_fid_parse()
stores the address of the first invalid character in
.IR *endptr ,
or the character immediately following the end of the parsed FID.
.SH RETURN VALUES
.B llapi_fid_parse()
returns:
.TP
0
on success,
.TP
<0
a negative errno on failure and sets errno.
.SH ERRORS
.TP 15
.B -EINVAL
.I fidstr
is NULL or does not contain a valid FID format.
.TP 15
.B -ERANGE
.I fidstr
contains numeric values that exceed valid values for a component.
.SH EXAMPLES
.nf
#include <lustre/lustreapi.h>
\&
int main(int argc, char *argv[])
{
        char fidstr = "[0x200000004:0x2:0x0] [0x200000400:0x345:0x0]";
        struct lu_fid fid1, fid2;
        char *end;
        int rc;
\&
        fidstr = argv[1];
        rc = llapi_fid_parse(fidstr, &fid1, &end);
        if (rc < 0) {
                fprintf(stderr, "invalid first FID '%s': %s\\n",
                        fidstr, strerror(-rc));
                return -1;
        }
\&
        fidstr = end;
        rc = llapi_fid_parse(fidstr, &fid2, &end);
        if (rc < 0) {
                fprintf(stderr, "invalid second FID '%s': %s\\n",
                        fidstr, strerror(-rc));
                return -1;
        }
\&
        printf("fid1=" DFID " fid2="DFID"\\n", PFID(&fid1), PFID(&fid2));
        return 0;
}
.fi
.SH AVAILABILITY
.B llapi_fid_parse()
is part of the
.BR lustre (7)
user application interface library since release 2.14.0
.\" Added in commit v2_13_50-48-g21d671b3af
.SH SEE ALSO
.BR llapi_path2parent (3),
.BR lustre (7),
.BR lustreapi (7)
