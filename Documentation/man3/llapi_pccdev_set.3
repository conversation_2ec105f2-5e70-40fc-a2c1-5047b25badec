.TH LLAPI_PCCDEV_SET 3 2024-08-28 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_pccdev_set \- Add/delete a PCC backend on a client
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_pccdev_set(const char *" path ", const char *" cmd ");"
.fi
.SH DESCRIPTION
The function
.BR llapi_pccdev_set()
adds or deletes a PCC backend on the client with the mount point referenced by
.IR path .
The input argument
.IR cmd
could be in the following forms:
.TP
.B \(dq\&add $PCCPATH $PARAM\(dq
Add a PCC backend referenced by the HSM root path
.IR $PCCPATH .
.TP
.B \(dq\&del $PCCPATH\(dq
Delete a PCC backend referenced by the HSM root path
.IR $PCCPATH .
.TP
.B \(dq\&clear\(dq
Clear and remove all PCC backends on a client.
.SH RETURN VALUES
.B llapi_pccdev_set()
return 0 on success or a negative errno value on failure.
.SH ERRORS
.TP 15
.B -ENOMEM
Insufficient memory to complete operation.
.TP
.B -EFAULT
Memory region is not properly mapped.
.TP
.B -EINVAL
One or more invalid arguments are given.
.TP
.B -EOPNOTSUPP
PCC backend operation is not supported.
.SH AVAILABILITY
.B llapi_pccdev_set()
is part of the
.BR lustre (7)
user application interface library since release 2.13.0
.\" Added in commit v2_12_53-113-gf172b11688
.SH SEE ALSO
.BR lustreapi (7)
