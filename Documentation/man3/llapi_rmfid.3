.TH LLAPI_RMFID 3 2024-08-28 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_rmfid, llapi_rmfid_at \- Remove files by their FIDs in Lustre.
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_rmfid(const char *" path ", struct fid_array *" fa );
.BI "int llapi_rmfid_at(int " fd ", struct fid_array *" fa );
.fi
.SH DESCRIPTION
.BR llapi_rmfid()
tries to remove
.I fa->fa_nr
Lustre files by FIDs stored in
.I fa->fa_fids
All file's hardlinks are subject to removal. This functionality is available
only for root or regular users on filesystems mounted with
.I user_fid2path
mount option to delete files that they own and are in a directory in which
they have write permission.
.P
.BR llapi_rmfid_at()
Is similar to
.I llapi_rmfid.
It tries to remove Lustre files by FIDs stored in
.I fa->fa_fids
where path or device is pointed by an already verified
.I fd.
.SH RETURN VALUES
.B llapi_rmfid()
and
.B llapi_rmfid_at()
return 0 on success or a negative errno value on failure. Result for each file
is stored in the corresponding
.I fa->fa_fid[N].f_ver
.SH ERRORS
.TP 15
.B -ENOENT
.I file
does not exist.
.TP
.B -EBUSY
file is open and can't be removed
.TP
.B -EPERM
The file cannot be open by user or CAP_DAC_READ_SEARCH is not granted.
.TP
.B -EINVAL
Invalid FID is passed
.TP
.B -ENOMEM
Not enough memory to process the request
.TP
.B -ENOTTY
.I file
may not point to a Lustre filesystem mountpoint
.SH AVAILABILITY
.B llapi_rmfid()
and
.B llapi_rmfid_at()
is part of the
.BR lustre (7)
user application interface library since release 2.13.0
.\" Added in commit 2.12.56-71-g1fd63fcb04
.SH SEE ALSO
.BR llapi_rmfid_at (3),
.BR lustreapi (7)
