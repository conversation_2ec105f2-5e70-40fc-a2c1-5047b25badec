.TH LLAPI_FID_TO_HANDLE 3 2024-08-23" "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_fid_to_handle \- allocate an NFS handle for File Identifier
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_fid_to_handle(struct file_handle **" handle ", struct lu_fid *" fid ");"
.fi
.SH DESCRIPTION
.BR llapi_fid_to_handle()
allocates an NFS file
.I handle
for the File Identifier given by
.I fid
for later use by
.BR open_by_handle_at (2).
This is similar to the
.BR name_to_handle_at (2)
function, and is useful for Lustre-specific tools and interfaces.  While the
.BR llapi_fid_to_handle (3)
function does not require root access itself, the
.BR open_by_handle_at (2)
function
.B does
require root access since this bypasses all pathname-derived file access
permissions.
The memory allocated for
.I handle
and returned to the caller must be freed with
.IR free (2).
.SH RETURN VALUES
returns 0 on success or a negative errno on failure.
.SH ERRORS
.TP 15
.B -ENOMEM
No memory was available for allocating
.IR handle .
.SH AVAILABILITY
.B llapi_fid_to_handle()
is part of the
.BR lustre (7)
user application interface library since release 2.16.0
.\" Added in commit v2_15_52-154-gbdf7788d19
.SH SEE ALSO
.BR name_to_handle (2),
.BR open_by_handle_at (2),
.BR free (3),
.BR llapi_fid_parse (3),
.BR llapi_fid2path (3),
.BR llapi_path2fid (3),
.BR lustreapi (7),
