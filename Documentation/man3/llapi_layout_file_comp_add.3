.TH LLAPI_LAYOUT_FILE_COMP_ADD 3 2024-08-27 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_layout_file_comp_add \- add one or more components into an existing file.
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_layout_file_comp_add(const char *" path ",
.BI "                               const struct llapi_layout *" layout );
.fi
.SH DESCRIPTION
Add component(s) specified in
.I layout
into the existing file
.IR path .
.SH RETURN VALUES
Return 0 on success, or -1 if an error occurred (in which case, errno is
set appropriately).
.SH ERRORS
.TP 15
.SM EINVAL
An invalid argument was specified.
.TP
.B ENOENT
.I path
doesn't exist.
.SH AVAILABILITY
.B llapi_layout_file_comp_add()
is part of the
.BR lustre (7)
user application interface library since release 2.10.0
.\" Added in commit v2_9_55_0-18-gc4702b7443
.SH SEE ALSO
.BR llapi_layout_alloc (3),
.<PERSON> llapi_layout_comp_add (3),
.<PERSON> llapi_layout_comp_del (3),
.<PERSON> llapi_layout_file_open (3),
.BR llapi_layout (7),
.BR lustreapi (7)
