.TH LLAPI_PCCDEV_GET 3 2024-08-28 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_pccdev_get \- List all PCC backends on a client
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_pccdev_get(const char *" path ");"
.fi
.SH DESCRIPTION
The function
.BR llapi_pccdev_get()
lists all PCC backends on the client with the mount point referenced by
.IR path ,
and output the results to stdout in YAML format.
.SH RETURN VALUES
.B llapi_pccdev_get()
return 0 on success or a negative errno value on failure.
.SH ERRORS
.TP 15
.B -ENOMEM
Insufficient memory to complete operation.
.TP
.B -EFAULT
Memory region is not properly mapped.
.TP
.B -EINVAL
One or more invalid arguments are given.
.TP
.B -EOPNOTSUPP
PCC backend operation is not supported.
.SH AVAILABILITY
.B llapi_pccdev_get()
is part of the
.BR lustre (7)
user application interface library since release 2.13.0
.\" Added in commit v2_12_53-113-gf172b11688
.SH SEE ALSO
.BR llapi_pccdev_set (3),
.BR lustreapi (7)
