.TH LLAPI_LAYOUT_STRIPE_SIZE_GET 3 2024-08-27 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_layout_stripe_size_get, llapi_layout_stripe_size_set \- get or set the stripe size of a Lustre file
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_layout_stripe_size_get(const struct llapi_layout *" layout ",
.BI "                                 uint64_t *" stripe_size );
.PP
.BI "int llapi_layout_stripe_size_set(struct llapi_layout *" layout ",
.BI "                                 uint64_t " stripe_size );
.fi
.SH DESCRIPTION
The stripe size indicates how much data to write to one OST before
moving to the next OST.
.PP
.B llapi_layout_stripe_size_get()
stores into
.I stripe_size
the stripe size of
.IR layout .
.PP
.B llapi_layout_stripe_size_set()
sets the stripe size of
.I layout
to
.IR stripe_size .
.PP
A
.I stripe_size
value of
.B LLAPI_LAYOUT_DEFAULT
means that the default stripe size will be used.
.SH RETURN VALUES
.B llapi_layout_stripe_size_get()
and
.B llapi_layout_stripe_size_set()
return 0 on success, or -1 if an error occurred (in which case, errno is
set appropriately).
.SH ERRORS
.TP 15
.B EINVAL
An invalid argument was specified.
.SH AVAILABILITY
.B llapi_layout_stripe_size_get()
and
.B llapi_layout_stripe_size_set()
is part of the
.BR lustre (7)
user application interface library since release 2.7.0
.\" Added in commit v2_6_51_0-23-g3d3a37c9c8
.SH SEE ALSO
.BR llapi_layout_alloc (3),
.BR llapi_layout_extension_size_get (3),
.BR llapi_layout_extension_size_set (3),
.BR llapi_layout_file_open (3),
.BR llapi_layout (7),
.BR lustreapi (7)
