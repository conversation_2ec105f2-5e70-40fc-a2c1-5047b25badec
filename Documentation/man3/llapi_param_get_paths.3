.TH LLAPI_PARAM_GET_PATHS 3 2024-08-27 "Lustre User API" "Lustre Library Function"
.SH NAME
llapi_param_get_paths \- get a list of Lustre parameter file paths that match the given pattern
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_param_get_paths(const char *" pattern ", glob_t *" paths );
.fi
.SH DESCRIPTION
The
.B llapi_param_get_paths(\|)
function searches the locations in the filesystem that expose
Lustre parameters. All file names that match
.I pattern
and are in
the correct locations in the file system are returned.
.SH RETURN VALUES
.TP
.B 0
on success with the parameter paths stored in
.IR paths .
.TP
-ve
error code on failure and sets errno appropriately.
.SH AVAILABILITY
.B llapi_param_get_paths()
is part of the
.BR lustre (7)
user application interface library since release 2.14.0
.\" Added in commit v2_13_53-8-g9b44cf70a9
.SH SEE ALSO
.BR llapi_param_get_value (3),
.<PERSON> lustreapi (7),
.BR lctl-get_param (8)
