.TH LLAPI_LAYOUT_ALLOC 3 2024-08-27 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_layout_alloc, llapi_layout_free \- allocate and destroy struct llapi_layout objects.
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "struct llapi_layout *llapi_layout_alloc(void);"
.PP
.BI "void llapi_layout_free(struct llapi_layout *"layout );
.fi
.SH DESCRIPTION
.B llapi_layout_alloc()
returns a pointer to a newly-allocated
.BR "struct llapi_layout" .
The
.B struct llapi_layout
is an opaque entity containing the layout information for a file in a
Lustre filesystem.  Its internal structure should not be directly
accessed by an application.  It may be used in
subsequent calls to the functions referenced in the
.BR llapi_layout (7)
manual page to modify layout attributes and create files with the given
layout.
.PP
The returned
.B struct llapi_layout
object is initialized with default attribute values that will affect the
standard Lustre behavior for assigning layouts to newly-created files.
These values may be modified using the group of
functions in
.B struct llapi_layout
API whose names end with
.BR _set .
The pointer should be freed with
.B llapi_layout_free()
when it is no longer needed.
.PP
.B llapi_layout_free()
frees the memory associated with
.IR layout .
.SH RETURN VALUES
.B llapi_layout_alloc()
returns a valid pointer on success or
.B NULL
on failure with
.B errno
set to an approporiate error code.
.PP
.B llapi_layout_free()
returns no value.
.SH ERRORS
.TP 15
.B ENOMEM
Insufficient storage space is available.
.SH AVAILABILITY
.B llapi_layout_alloc()
and
.B llapi_layout_free()
are part of the
.BR lustre (7)
user application interface library since release 2.7.0.
.\" Added in commit v2_6_51_0-23-g3d3a37c9c8
.SH SEE ALSO
.BR llapi_layout_file_create (3),
.BR llapi_layout (7),
.BR lustreapi (7)
