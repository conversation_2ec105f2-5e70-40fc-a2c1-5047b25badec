.TH LLAPI_LAYOUT_COMP_FLAGS_GET 3 2024-08-27 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_layout_comp_flags_get, llapi_layout_comp_flags_set, llapi_layout_comp_flags_clear \- get, set or clear the flags of a layout component.
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_layout_comp_flags_get(const struct llapi_layout *" layout ",
.BI "                                uint32_t *" flags );
.PP
.BI "int llapi_layout_comp_flags_set(struct llapi_layout *" layout ",
.BI "                                uint32_t " flags );
.PP
.BI "int llapi_layout_comp_flags_clear(struct llapi_layout *" layout ",
.BI "                                  uint32_t " flags );
.fi
.SH DESCRIPTION
Layout component flags are used to indicate the status of the component, only
LCME_FL_INIT is supported now, which indicates the OST objects of the component
have been initialized.
.PP
.BR llapi_layout_comp_flags_get (3)
gets the flags of
.I layout
and stores it into
.IR flags .
.PP
.BR llapi_layout_comp_flags_set (3)
sets the specified
.I flags
to the
.IR layout .
.PP
.BR llapi_layout_comp_flags_clear (3)
clears the specified
.I flags
from the
.IR layout .
.SH RETURN VALUES
Return 0 on success, or -1 if an error occurred (in which case, errno is
set appropriately).
.SH ERRORS
.TP 15
.B EINVAL
An invalid argument was specified.
.SH AVAILABILITY
.BR llapi_layout_comp_flags_get(),
.B llapi_layout_comp_flags_set()
and
.B llapi_layout_comp_flags_clear()
are part of the
.BR lustre (7)
user application interface library since release 2.10.0
.\" Added in commit v2_9_55_0-18-gc4702b7443
.SH SEE ALSO
.BR llapi_layout_alloc (3),
.BR llapi_layout_file_open (3),
.BR llapi_layout (7),
.BR lustreapi (7)
