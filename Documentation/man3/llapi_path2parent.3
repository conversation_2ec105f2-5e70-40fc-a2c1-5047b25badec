.TH LLAPI_PATH2PARENT 3 2024-08-28 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_path2parent, llapi_fd2parent \- Retrieve <parent FID>/name(s) for an entry in Lustre.
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_path2parent(const char *" path ", unsigned int " linkno ,
.BI "                      struct lu_fid *" parent_fid ", char *" name ,
.BI "                      size_t " name_size );
.PP
.BI "int llapi_fd2parent(int " fd ", unsigned int " linkno ,
.BI "                    struct lu_fid *" parent_fid ", char *" name ,
.BI "                    size_t " name_size );
.fi
.SH DESCRIPTION
.BR llapi_path2parent()
for link number
.I linkno
stores the FID of the parent directory into
.I parent_fid
and the NUL-terminated name of the entry into the buffer
.I name
which is expected to be of size
.IR name_size .
If
.I linkno
should be initialized to
.B -1
when starting to iterate over all links of a file.  The function
.B llapi_fd2parent()
behaves similarly except that it operates on an open file descriptor
instead of a path.
.SH RETURN VALUES
0 is returned on success or a negative errno value on failure.
.SH ERRORS
.TP 15
.B -ENODATA
linkno has reached the total link count of the file.
.TP
.B -ENOTTY
.I path
or
.I fd
does not reside on a Lustre filesystem.
.TP
.B -ENOENT
.I path
does not exist.
.TP
.B -EINVAL
An invalid argument was specified.
.TP
.B -EOVERFLOW
The given buffer was too small to hold the pathname.
.TP
.B -EPERM
The file cannot be open by user or CAP_DAC_READ_SEARCH is not granted.
.SH AVAILABILITY
.B llapi_path2parent()
and
.B llapi_fd2parent()
are part of the
.BR lustre (7)
user application interface library since release 2.4.0
.\" Added in commit 2.3.53-7-gf715e4e298
.SH SEE ALSO
.BR lfs-path2fid (1),
.BR llapi_fid2path (3),
.BR llapi_path2fid (3),
.BR lustreapi (7)
