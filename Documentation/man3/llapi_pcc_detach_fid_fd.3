.TH LLAPI_PCC_DETACH_FID_FD 3 2024-08-28 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_pcc_detach_fid_fd, llapi_pcc_detach_fid, llapi_pcc_detach_fid_str, llapi_pcc_detach_file \- detach the given file from PCC
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_pcc_detach_fid_fd(int " dirfd ", const struct lu_fid *" fid ,
.BI "                            __u32 " flags );
.PP
.BI "int llapi_pcc_detach_fid(const char *" mntpath ",
.BI "                         const struct lu_fid *" fid ", __u32 " flags );
.PP
.BI "int llapi_pcc_detach_fid_str(const char *" mntpath ,
.BI "                             const char *" fidstr ", __u32 " flags );
.PP
.BI "int llapi_pcc_detach_file(const char *" path ", __u32 " flags );
.fi
.SH DESCRIPTION
.BR llapi_pcc_detach_fid_fd() ,
.BR llapi_pcc_detach_fid() ,
.BR llapi_pcc_detach_fid_str() ,
and
.B llapi_pcc_detach_file()
detach a cached file from PCC by an ioctl on the directory or the file itself.
The file is referenced by
.IR fid ,
.IR fidstr ,
or
.IR path .
The directory, which is usually a mount point that the copytool
already has opened, is referenced by
.IR dirfd ,
.IR mntpath ,
.IR path .
The detach flags are specified by
.IR flags
argument, which is a
.B enum lu_pcc_detach_flags
data structure, which contains the following values:
.nf
	PCC_DETACH_FL_NONE			= 0x0,
	PCC_DETACH_FL_UNCACHE		= 0x01,
	PCC_DETACH_FL_KNOWN_READWRITE	= 0x02,
	PCC_DETACH_FL_KNOWN_READONLY	= 0x04,
	PCC_DETACH_FL_CACHE_REMOVED	= 0x08,
.fi
.TP
PCC_DETACH_FL_NONE
means that detach the file from PCC yet retain the data copy on PCC backend.
.TP
PCC_DETACH_FL_UNCACHE
means that remove the PCC copy after detach.
.TP
PCC_DETACH_FL_KNOWN_READWRITE
means that it is known that the file was once cached as PCC-RW.
.TP
PCC_DETACH_FL_KNOWN_READONLY
means that it is known that the file was once cached as PCC-RO.
.TP
PCC_DETACH_FL_CACHE_REMOVED
indicates that PCC cached copy is removed. It is used to tell the user space
caller that the file is detached and the corresponding PCC copy is removed.
.SH RETURN VALUES
.BR llapi_pcc_detach_fid_fd() ,
.BR llapi_pcc_detach_fid() ,
.BR llapi_pcc_detach_fid_str() ,
and
.B llapi_pcc_detach_file()
return 0 on success or a negative errno value on failure.
.SH ERRORS
.TP 15
.B -ENOMEM
Insufficient memory to complete operation.
.TP
.B -EFAULT
Memory region is not properly mapped.
.TP
.B -EINVAL
One or more invalid arguments are given.
.TP
.B -EOPNOTSUPP
PCC state operation is not supported.
.TP
.B -ENOTTY
File does not reside on a Lustre filesystem.
.TP
.B -ENOENT
.I path
does not exist.
.SH AVAILABILITY
.BR llapi_pcc_detach_fid_fd() ,
.BR llapi_pcc_detach_fid() ,
.BR llapi_pcc_detach_fid_str() ,
and
.B llapi_pcc_detach_file()
are part of the
.BR lustre (7)
user application interface library since release 2.13.0
.\" Added in commit v2_12_53-113-gf172b11688
.SH SEE ALSO
.BR llapi_pcc_attach (3),
.BR lustreapi (7)
