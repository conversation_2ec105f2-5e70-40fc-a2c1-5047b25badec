.TH LLAPI_LAYOUT_COMP_USE 3 2024-08-27 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_layout_comp_use \- change current component to relative position
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_layout_comp_use(struct llapi_layout *" layout ",
.BI "                          enum llapi_layout_comp_use *" pos );
.fi
.SH DESCRIPTION
Changes the currently active component of
.I layout
to the relative position given by
.IR pos .
Possible values for
.I pos
are:
.PP
.BR LLAPI_LAYOUT_COMP_USE_FIRST :
The first component of the layout.
.PP
.BR LLAPI_LAYOUT_COMP_USE_LAST :
The last component of the layout.
.PP
.BR LLAPI_LAYOUT_COMP_USE_NEXT :
The next component after the current one.
.PP
.BR LLAPI_LAYOUT_COMP_USE_PREV :
The previous component before the current one.
.SH RETURN VALUES
Return 0 on success, and 1 when there is no next or previous component.
Otherwise, if an error occurred -1 is returned and
.I errno
is set appropriately.
.SH ERRORS
.TP 15
.B EINVAL
An invalid argument was specified.
.TP
.B ENOENT
No such component ID exists.
.SH AVAILABILITY
.B llapi_layout_comp_use()
is part of the
.BR lustre (7)
user application interface library since release 2.10.0
.\" Added in commit v2_9_55_0-18-gc4702b7443
.SH SEE ALSO
.BR llapi_layout_alloc (3),
.BR llapi_layout_comp_del (3),
.BR llapi_layout_comp_use_id (3),
.BR llapi_layout_file_open (3),
.BR llapi_layout (7),
.BR lustreapi (7)
