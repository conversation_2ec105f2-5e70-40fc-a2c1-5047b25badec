.TH LLAPI_PROJECT_GETPRJID 3 2025-07-01 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_project_getprjid \- read project matching a project ID to a buffer
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.sp
.BI "int llapi_project_get(struct ll_project *" lprj ", const unsigned int " prjid );
.fi
.SH DESCRIPTION
The
.B llapi_project_getprjid()
function reads the contents of the
.B LIBLUSTREAPI_PROJECT
file until an entry matches the provided
.IR prjid ,
and fills in the remaining space with the information provided in the file.
The buffer should be preallocated.
.SH RETURN VALUES
.TP
.B 0
on success
.TP
-ve
error code on failure and sets errno appropriately.
.SH ERRORS
.TP 15
.B -EINVAL
if opening the file fails, or
.I lprj
is NULL or
.I lprj->lprj_size
is invalid.
.TP
.B -ENOENT
if no entry was found in
.I LIBLUSTREAPI_PROJECT
with information matching
.IR prjid .
.SH AVAILABILITY
.B llapi_project_getprjid()
is part of the
.BR lustre (7)
user application interface library since release 2.17.0
.\" Added in commit v2_16_55-17-g75c6d5636d
.SH SEE ALSO
.BR lfs_project (1),
.BR llapi_project_close (3),
.BR llapi_project_fgetnam (3),
.BR llapi_project_fgetprjid (3),
.BR llapi_project_get (3),
.BR llapi_project_getnam (3),
.BR llapi_project_open (3),
.BR llapi_project_put (3),
.BR projid (5),
.BR lustreapi (7)
