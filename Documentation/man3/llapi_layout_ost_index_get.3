.TH LLAPI_LAYOUT_OST_INDEX_GET 3 2024-08-27 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_layout_ost_index_get, llapi_layout_ost_index_set \- get or set the OST index of a stripe of a Lustre file
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_layout_ost_index_get(const struct llapi_layout *" layout ,
.BI "                               int " stripe_number ", uint64_t *" ost_index );
.PP
.BI "int llapi_layout_ost_index_set(struct llapi_layout *" layout ,
.BI "                               int " stripe_number ", uint64_t " ost_index );
.fi
.SH DESCRIPTION
.B llapi_layout_ost_index_get()
stores into
.I ost_index
the index number of the Lustre OST associated with
.I stripe_number
in
.IR layout .
.PP
.B llapi_layout_ost_index_set()
sets the OST index of
.I stripe_number
in
.I layout
to
.IR ost_index .
This allows an application to control which OSTs will be used to
allocate storage for a file.  Setting the starting OST index for a file
.RI ( stripe_number =0)
is supported with all versions of Lustre, and other stripes in the file
will be assigned sequentially by the filesystem.  Setting the OST indices
other than stripe 0 is only supported with Lustre 2.7 and later, and will
return an error at file creation time for 2.6 and earlier.
.PP
It is an error to call
.B llapi_layout_ost_index_get()
with a
.I layout
that was not initialized with one of
.BR llapi_layout_get_by_fd() ,
.BR llapi_layout_get_by_fid() ,
or
.BR llapi_layout_get_by_path() .
.PP
An
.I ost_index
value of
.B LLAPI_LAYOUT_DEFAULT
is only valid for
.IR stripe_number =0
and means that an OST index will be automatically assigned by the
filesystem and will reset previously-set
.I stripe_number
assignments.
.SH RETURN VALUES
.B llapi_layout_ost_index_get()
and
.B llapi_layout_ost_index_set()
return 0 on success, or -1 if an error occurred (in which case, errno is
set appropriately).
.SH ERRORS
.TP 15
.B EINVAL
An invalid argument was specified.
.TP 15
.B EOPNOTSUPP
Attempted to set index of a stripe other than stripe 0.
.SH AVAILABILITY
.B llapi_layout_ost_index_get()
and
.B llapi_layout_ost_index_set()
are part of the
.BR lustre (7)
user application interface library since release 2.7.0
.\" Added in commit v2_6_51_0-23-g3d3a37c9c8
.SH SEE ALSO
.BR llapi_layout_alloc (3),
.BR llapi_layout_file_open (3),
.BR llapi_layout (7),
.BR lustreapi (7)
