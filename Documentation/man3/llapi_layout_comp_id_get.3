.TH LLAPI_LAYOUT_COMP_ID_GET 3 2024-08-27 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_layout_comp_id_get \- get the ID of a layout component.
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_layout_comp_id_get(const struct llapi_layout *" layout ",
.BI "                             uint32_t *" comp_id );
.fi
.SH DESCRIPTION
Return the unique numeric ID
.I comp_id
of the currently active component of
.IR layout .
The ID of a component is a unique numeric identifier for the component
within the layout of each file, and no age, ordering, relative position,
or other semantics are implied by the component ID.  If a file's layout
is modified a large number of times, the component ID may be re-used
but will always be unique within a single file's layout.
.SH RETURN VALUES
Return 0 on success.  Return -1 if an error occurred and set errno
appropriately.
.SH ERRORS
.TP 15
.B EINVAL
An invalid argument was specified.
.SH AVAILABILITY
.B llapi_layout_comp_id_get()
is part of the
.BR lustre (7)
user application interface library since release 2.10.0
.\" Added in commit v2_9_55_0-18-gc4702b7443
.SH SEE ALSO
.BR llapi_layout_alloc (3),
.BR llapi_layout_comp_use_id (3),
.BR llapi_layout_file_open (3),
.BR llapi_layout (7),
.BR lustreapi (7)
