.TH LLAPI_SEARCH_TGT 3 2024-08-28 "Lustre User API" "Lustre Library Functions"
.SH NAME
llapi_search_tgt, llapi_search_mdt, llapi_search_ost \- verify OST or MDT is in specified pool
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.PP
.BI "int llapi_search_tgt(const char *" fsname ", const char *" pool_name ,
.BI "                     const char *" tgt_name ", bool " is_mdt );
.PP
.BI "int llapi_search_mdt(const char *" fsname ", const char *" pool_name ,
.BI "                     const char *" tgt_name );
.PP
.BI "int llapi_search_ost(const char *" fsname ", const char *" pool_name ,
.BI "                     const char *" tgt_name );
.fi
.SH DESCRIPTION
.B llapi_search_tgt()
verifies that the specified
.I tgt_name
is a valid MDT or OST (depending on whether
.I is_mdt
is
.B true
or
.BR false ,
respectively) in the filesystem named
.I fsname
and (if specified) in the pool named
.I pool_name
of that filesystem. At least
.I fsname
must be non-NULL, but
.I pool_name
may be
.B NULL
if just checking for the existence of the target, and
.I tgt_name
may be
.BR NULL ,
if only checking the existence of
.IR pool_name .
.PP
The
.B llapi_search_mdt()
and
.B llapi_search_ost()
functions are simple convenience/compatibility wrappers of
.B llapi_search_tgt()
that set
.I is_mdt
appropriately for the target type.
.SH RETURN VALUES
.B llapi_search_tgt()
returns 1 if
.I tgt_name
(or
.I pool_name
if
.I tgt_name
is
.BR NULL )
is found in the specified pool
(or
.I filesystem
if
.I pool_name
is
.BR NULL ).
It returns 0 if
.I tgt_name
.RI ( pool_name )
if it is not found in the specified pool
.RI ( filesystem ).
A negative errno is returned if an error occurred, in which case errno
is also set appropriately.
.SH ERRORS
.TP 15
.B EINVAL
.I fsname
is NULL or zero-length, or both
.I pool_name
and
.I tgt_name
are
.BR NULL .
.TP
.B ENOENT
.I pool_name
is specified but is not part of
.IR fsname .
.SH AVAILABILITY
.BR llapi_search_tgt() ,
.B llapi_search_mdt()
and
.B llapi_search_ost()
is part of the
.BR lustre (7)
user application interface library since release 2.4.0
.\" Added in commit 2.3.53-7-gf715e4e298
.SH SEE ALSO
.BR llapi_pool_name_verify (3),
.BR lustre (7),
.BR lustreapi (7)
