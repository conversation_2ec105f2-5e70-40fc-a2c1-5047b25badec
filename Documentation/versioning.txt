Lustre versioning
=================

This document describes versioning of source and binaries for Lustre.

Versions
========

The Lustre version is generated by the LUSTRE-VERSION-GEN script at the
top level of the lustre source tree.  The script prints the version to
stdout and also updates the file at the top of the source tree named
LUSTRE-VERSION-FILE to contain the current version (if the file does not
already exist, or if it contains an out-of-date version).

LUSTRE-VERISON-GEN is called when the autotools are initialized,
either when autogen.sh is run or when autoreconf runs.  The stdout
of the script is used as the version option of AC_INIT in configure.ac.
This ensures that the AC_PACKAGE_VERSION m4 macro, the PACKAGE_VERSION
output variable, and the PACKAGE_VERSION preprocessor symbol are all
correctly set to the current version.  Likewise AC_PACKAGE_STRING and
PACKAGE_STRING will contain the current version.

If the lustre tree is in a git working directory, LUSTRE-VERSION-GEN
uses "git --describe" to generate the version number.  If the tree is
not in a git working directory but a LUSTRE-VERSION-FILE file exists
(such as in the tarball created by "make dist"), the script falls back
to using the value in the file.  Finally, if it is not a git working
directory and the LUSTRE-VERSION-FILE also does not exist (such as when
the source tree is retrieved with "git archive --remote") LUSTRE-VERSION-GEN
will fall back to using its own internal DEFAULT_VERSION variable.

Tagging Guidelines - General
============================

It is best practice to edit the DEFAULT_VERSION in LUSTRE-VERSION-GEN
before creating a tag of the same name.  Public community versions
should have the form "1.1.1" or "*******".

Tagging Guildines - Third Party
===============================

Third parties (e.g. vendors, labs, universities) that wish to tag their
own releases should append a version that unamiguously identifies the
release for their organization.  This is done by adding an underscore,
and then a unique version to the tag.

For instance, lets consider a vendor named Acme.  They are releasing a
their second patched version of Lustre 2.8.0 to their customers.  They
would set the version to something like the following:

  2.8.0_2.acme

The third party subversion string can contain alphanumerics, underscores,
and periods.

Strictly speaking, if one is certain that the third party release code
will only be built from inside a git working directory, only a git tag
need be made.  In other words, creating a git commit to update the
DEFAULT_VERSION in LUSTRE-VERSION-GEN is a not always necessary.
LUSTRE-VERSION-GEN can retrieve the version solely using git, from the
git tag and git describe information.

Releases
========

Major releases should get a 3-digit version x.y.0, and if a maintenance
release is needed it will increment the 3rd digit.  During development,
versions start at x.y.50 and increment toward the x.(y+1).0 release.

Usage:
------

New numbers are used as follows:

1. major:
 - increased when major new functionality becomes available
2. minor:
 - for each new release with new functionality
3. patch:
 - for a stable maintenance release or development snapshot
4. fix:
 - minor fix release
