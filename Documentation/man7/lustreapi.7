.TH LUSTREAPI 7 2024-08-28 Lu<PERSON><PERSON> "The Lustre API library"
.SH NAME
lustreapi \- The Lustre API library
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.fi
.SH DESCRIPTION
The lustreapi library provides functions to access and/or modify
settings specific to the Lustre filesystem (allocation policies,
quotas, file layouts, etc).  See the referenced man pages for details.
.SH AUTHORS
Lustre contributors
.SH AVAILABILITY
.B lustreapi
is part of the
.B lustre (8)
filesystem package since release 0.10.0
.\" Added in release 0.9.1
.SH SEE ALSO
.BR llapi_changelog_clear (3),
.<PERSON> llapi_changelog_fini (3),
.BR llapi_changelog_free (3),
.BR llapi_changelog_get_fd (3),
.BR llapi_changelog_in_buf (3),
.BR llapi_changelog_recv (3),
.BR llapi_changelog_set_xflags (3),
.BR llapi_changelog_start (3),
.BR llapi_create_volatile_param (3),
.BR llapi_fd2parent (3),
.BR llapi_fid_parse (3),
.BR llapi_fid2path (3),
.BR llapi_file_create (3),
.BR llapi_file_get_stripe (3),
.BR llapi_file_open (3),
.BR llapi_group_lock (3),
.BR llapi_group_unlock (3),
.BR llapi_heat_get (3),
.BR llapi_heat_set (3),
.BR llapi_hsm_action_begin (3),
.BR llapi_hsm_action_end (3),
.BR llapi_hsm_action_get_dfid (3),
.BR llapi_hsm_action_get_fd (3),
.BR llapi_hsm_action_progress (3),
.BR llapi_hsm_copytool_get_fd (3),
.BR llapi_hsm_copytool_recv (3),
.BR llapi_hsm_copytool_register (3),
.BR llapi_hsm_copytool_unregister (3),
.BR llapi_hsm_state_get (3),
.BR llapi_hsm_state_set (3),
.BR llapi_ladvise (3),
.BR llapi_layout_alloc (3),
.BR llapi_layout_comp_add (3),
.BR llapi_layout_comp_del (3),
.BR llapi_layout_comp_extent_get (3),
.BR llapi_layout_comp_extent_set (3),
.BR llapi_layout_comp_flags_clear (3),
.BR llapi_layout_comp_flags_get (3),
.BR llapi_layout_comp_flags_set (3),
.BR llapi_layout_comp_id_get (3),
.BR llapi_layout_comp_use (3),
.BR llapi_layout_comp_use_id (3),
.BR llapi_layout_file_comp_add (3),
.BR llapi_layout_file_comp_del (3),
.BR llapi_layout_file_create (3),
.BR llapi_layout_file_open (3),
.BR llapi_layout_free (3),
.BR llapi_layout_get_by_fd (3),
.BR llapi_layout_get_by_fid (3),
.BR llapi_layout_get_by_path (3),
.BR llapi_layout_get_by_xattr (3),
.BR llapi_layout_ost_index_get (3),
.BR llapi_layout_ost_index_set (3),
.BR llapi_layout_pattern_get (3),
.BR llapi_layout_pattern_set (3),
.BR llapi_layout_pool_name_get (3),
.BR llapi_layout_pool_name_set (3),
.BR llapi_layout_stripe_count_get (3),
.BR llapi_layout_stripe_count_set (3),
.BR llapi_layout_stripe_size_get (3),
.BR llapi_layout_stripe_size_set (3),
.BR llapi_path2fid (3),
.BR llapi_path2parent (3),
.BR llapi_quotactl (3),
.BR llapi_rmfid (3),
.BR llapi_rmfid_at (3),
.BR llapi_root_path_open (3),
.BR llapi_search_rootpath (3),
.BR llapi_search_rootpath_by_dev (3),
.BR llapi_search_tgt (3),
.BR lustre (7)
