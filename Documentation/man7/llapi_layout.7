.TH LLAPI_LAYOUT 7 2024-08-27 "Lustre User API" "Lustre Miscellaneous Information"
.SH NAME
llapi_layout \- abstract interface to the layout of a Lustre file
.SH SYNOPSIS
.nf
.B #include <lustre/lustreapi.h>
.fi
.SH DESCRIPTION
The
.B llapi_layout
family of functions provides an abstract interface to
manipulating the layout information of a file in a Lustre filesystem.
Layouts are represented by the opaque data type
.B struct llapi_layout
which is passed as a handle to the various functions.
.PP
A layout has a number of attributes that describe how a file's data are
stored in the filesystem.  These include stripe count, stripe size, RAID
pattern, pool name, and the OST index associated with each stripe. In more
complex layouts, a file may have multiple components that describe a different
layout for separate regions of the file. Refer to the Lustre Operations Manual
for detailed descriptions of these attributes.  For each attribute, there
exists a pair of functions with the suffixes
.B _get
and
.B _set
that are used to read and assign the attribute value in a given layout.
.PP
Using this interface to create a file might consist of the following steps.
.IP \[bu]
Allocate
.B struct llapi_layout
with
.BR llapi_layout_alloc (3).
.IP \[bu]
Assign attribute values using the
.B llapi_layout_*_set (3)
functions.
.IP \[bu]
Create the file using
.BR llapi_layout_file_create (3).
.IP \[bu]
Free the layout memory using
.BR llapi_layout_free (3).
.PP
Similarly, these steps might be used to read a file layout:
.IP \[bu]
One can allocate and initialize a new
.B struct layout
from an existing file with one of the
.BR llapi_layout_get_by_path (3),
.BR llapi_layout_get_by_fd (3),
.BR llapi_layout_get_by_fid (3),
or
.BR llapi_layout_get_by_xattr (3)
functions.
.IP \[bu]
To access attribute values from
.B struct llapi_layout
use the
.B llapi_layout_*_get (3)
functions.
.IP \[bu]
To free the previously allocated layout memory use
.BR llapi_layout_free (3).
.PP
Using this interface to create a file with composite layout consists of the
following steps:
.IP \[bu]
Allocate first layout component with
.BR llapi_layout_alloc (3).
.IP \[bu]
Assign attribute values using the
.B llapi_layout_*_set (3)
functions.
.IP \[bu]
Allocate second layout component with
.BR llapi_layout_alloc (3).
.IP \[bu]
Add the second layout into the first one using
.BR llapi_layout_comp_add (3).
.IP \[bu]
Assign attribute values to second layout using the
.BR llapi_layout_*_set (3)
functions.
.IP \[bu]
Repeat above two steps to add more components.
.IP \[bu]
Create the file using
.BR llapi_layout_file_create (3).
.IP \[bu]
Free the layout memory using
.BR llapi_layout_free (3).
.SH BUGS
Setting the OST index number is only supported for stripe number 0.
.P
The RAID pattern may only be set to 0.
.SH EXAMPLES
Example 1: Create file with specified layout, then query the layout attributes.
.PP
.nf
{
	/* Allocate layout */
	struct llapi_layout *layout = llapi_layout_alloc();
\&
	/* Set attributes of layout */
	llapi_layout_stripe_count_set(layout, count);
	llapi_layout_stripe_size_set(layout, size);
\&
	/* Create file with specified layout */
	fd = llapi_layout_file_create(path, 0, 0640, layout);
\&
	/* Free layout */
	llapi_layout_free(layout);
\&
	/* Retrieve layout from file */
	layout = llapi_layout_get_by_path(path, 0);
\&
	/* Get attributes of layout */
	llapi_layout_stripe_size_get(layout, &size),
	llapi_layout_stripe_count_get(layout, &count);
\&
	/* Free layout */
	llapi_layout_free(layout);
}
.fi
.PP
Example 2: Create file with composite layout.
.PP
.nf
{
	struct llapi_layout *head, *comp;
\&
	/* Create first component */
	head = llapi_layout_alloc();
	llapi_layout_stripe_count_set(head, 1);
	llapi_layout_stripe_size_set(head, 1048576);
	llapi_layout_comp_extent_set(NULL, head, 0, 2097152); //[0, 2M)
\&
	/* Create the second component */
	comp = llapi_layout_alloc();
	llapi_layout_comp_add(head, comp);
	llapi_layout_comp_extent_set(head, comp, 2097152, 67108864); //[2M, 64M)
	llapi_layout_stripe_count_set(comp, 4);
\&
	/* Create the third component */
	comp = llapi_layout_alloc();
	llapi_layout_comp_add(head, comp);
	llapi_layout_comp_extent_set(head, comp, 67108864,
			     (uint64_t)-1); //[64M, EOF)
	llapi_layout_stripe_count_set(comp, LLAPI_LAYOUT_WIDE);
\&
	/* Create file with specified composite layout */
	fd = llapi_layout_file_create(path, 0, 0640, head);
\&
	/* Free layout */
	llapi_layout_free(head);
}
.fi
.PP
Example 3: Traverse components of a composite layout.
.PP
.nf
{
	/* Retrieve composite layout from file */
	layout = llapi_layout_get_by_path(path, 0);
\&
	/* Move cursor to the first component */
	rc = llapi_layout_comp_use(layout, LLAPI_LAYOUT_COMP_USE_FIRST);
\&
	/* Traverse all components */
	while (rc == 0) {
		/* Get attributes of each component */
		llapi_layout_stripe_count_get(comp, &count);
		llapi_layout_stripe_size_get(comp, &size);
		llapi_layout_comp_extent_get(layout, &start, &end);
\&
		/* Advance cursor */
		rc = llapi_layout_comp_use(layout, LLAPI_LAYOUT_COMP_USE_NEXT);
	};
\&
	/* Free layout */
	llapi_layout_free(layout);
}
.fi
.SH AVAILABILITY
.B llapi_layout
is part of the
.BR lustre (7)
user application interface library since release 2.7.0
.\" Added in commit v2_6_51_0-23-g3d3a37c9c8
.SH SEE ALSO
.BR lfs (1),
.BR lfs-setstripe (1),
.BR open (2),
.BR llapi_layout_alloc (3),
.BR llapi_layout_comp_add (3),
.BR llapi_layout_comp_del (3),
.BR llapi_layout_comp_extent_get (3),
.BR llapi_layout_comp_extent_set (3),
.BR llapi_layout_comp_flags_clear (3),
.BR llapi_layout_comp_flags_get (3),
.BR llapi_layout_comp_flags_set (3),
.BR llapi_layout_comp_id_get (3),
.BR llapi_layout_comp_use (3),
.BR llapi_layout_comp_use_id (3),
.BR llapi_layout_file_comp_add (3),
.BR llapi_layout_file_comp_del (3),
.BR llapi_layout_file_create (3),
.BR llapi_layout_file_open (3),
.BR llapi_layout_free (3),
.BR llapi_layout_get_by_fd (3),
.BR llapi_layout_get_by_fid (3),
.BR llapi_layout_get_by_path (3),
.BR llapi_layout_get_by_xattr (3),
.BR llapi_layout_ost_index_get (3),
.BR llapi_layout_ost_index_reset (3),
.BR llapi_layout_ost_index_set (3),
.BR llapi_layout_pattern_get (3),
.BR llapi_layout_pattern_set (3),
.BR llapi_layout_pool_name_get (3),
.BR llapi_layout_pool_name_set (3),
.BR llapi_layout_stripe_count_get (3),
.BR llapi_layout_stripe_count_set (3),
.BR llapi_layout_stripe_size_get (3),
.BR llapi_layout_stripe_size_set (3),
.BR lustre (7),
.BR lustreapi (7)
