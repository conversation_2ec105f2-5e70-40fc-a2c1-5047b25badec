.\" -*- nroff -*-
.\" Copyright (c) 2007, 2010, Oracle and/or its affiliates. All rights reserved.
.\" This file may be copied under the terms of the GNU Public License.
.\"
.\" Copyright (c) 2015, 2016, Intel Corporation.
.\"
.TH LUSTRE 7 2024-08-28 Lustre "A high-performance cluster file system"
.SH NAME
Lustre
.SH SYNOPSIS
A high-performance file system designed for Linux clusters.
.SH DESCRIPTION
.B Lustre
is a high-performance, massively-scalable, POSIX-compliant GPL network file
system that runs on the world's largest high-performance compute clusters.
.PP
Lustre filesystems are made up of multiple services typically distributed
across multiple server nodes.  Lustre clients can contact these server nodes
over multiple high-speed network fabrics via LNet, the Lustre Network
system.  Clients then present the filesystem at a mount point to userspace.
.PP
A Lustre filesystem is comprised of one or more
.IR MDTs ,
MetaData Targets, which store directory and file meta-information such as
file ownership, timestamps, access permissions, etc, and one or more
.IR OSTs ,
Object Storage Targets, which hold each file's data in one or more objects.
There is sometimes, but not always, a 1:1 mapping of regular files on a
client to OST objects on the server.  Each file may have a different
mapping of file offset to one or more OST object(s), known as the file
layout, that can be managed with the
.BR lfs-setstripe (1)
command.
.PP
Lustre and LNet are implemented as a series of Linux kernel modules, for both
servers and clients.  LNet networks are defined in the modprobe.conf file on
all nodes.  Lustre is started on the clients and servers using the
.BR mount (8)
command.
.SH COMMANDS
.TP
.BR mkfs.lustre (8)
Format a Linux block device for use as a Lustre server's backend storage
(aka target).
.TP
.BR tunefs.lustre (8)
Modify configuration information on a Lustre target disk.
.TP
.BR mount.lustre (8)
A helper program for
.BR mount (8)
that starts Lustre servers and clients mounts the client filesystem.
.TP
.BR lctl (8)
A low-level tool for administrators to control Lustre configuration.
.TP
.BR lfs (1)
A user-level tool to control Lustre-specific information for
individual files.
.SH BUGS
Please report all bugs https://jira.whamcloud.com/
.SH AVAILABILITY
.B The
.BR lustre (7)
filesystem packages are available for download at:
.br
https://downloads.whamcloud.com/
and GPL source code is available at:
.br
https://git.whamcloud.com/
.SH SEE ALSO
.BR lfs (1),
.BR lustreapi (7),
.BR lnet (7),
.BR lctl (8),
.BR llobdstat (8),
.BR llstat (8),
.BR llverdev (8),
.BR llog_reader (8),
.BR lshowmount (8),
.BR lustre_rsync (8),
.BR mkfs.lustre (8),
.BR mount.lustre (8),
.BR tunefs.lustre (8)
