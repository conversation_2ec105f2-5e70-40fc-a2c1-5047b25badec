Index: linux-3.10.0-957.27.2.el7/fs/ext4/ext4.h
===================================================================
--- linux-3.10.0-957.27.2.el7.orig/fs/ext4/ext4.h
+++ linux-3.10.0-957.27.2.el7/fs/ext4/ext4.h
@@ -1539,6 +1539,7 @@ enum {
 	EXT4_STATE_NO_EXPAND,		/* No space for expansion */
 	EXT4_STATE_DA_ALLOC_CLOSE,	/* Alloc DA blks on close */
 	EXT4_STATE_EXT_MIGRATE,		/* Inode is migrating */
+	EXT4_STATE_IAM,			/* Lustre IAM objects */
 	EXT4_STATE_DIO_UNWRITTEN,	/* need convert on dio done*/
 	EXT4_STATE_NEWENTRY,		/* File just added to dir */
 	EXT4_STATE_DIOREAD_LOCK,	/* Disable support for dio read
Index: linux-3.10.0-957.27.2.el7/fs/ext4/namei.c
===================================================================
--- linux-3.10.0-957.27.2.el7.orig/fs/ext4/namei.c
+++ linux-3.10.0-957.27.2.el7/fs/ext4/namei.c
@@ -58,8 +58,10 @@ struct buffer_head *ext4_append(handle_t
 
 	if (unlikely(EXT4_SB(inode->i_sb)->s_max_dir_size_kb &&
 		     ((inode->i_size >> 10) >=
-		      EXT4_SB(inode->i_sb)->s_max_dir_size_kb)))
-		return ERR_PTR(-ENOSPC);
+		      EXT4_SB(inode->i_sb)->s_max_dir_size_kb))) {
+		if (!ext4_test_inode_state(inode, EXT4_STATE_IAM))
+			return ERR_PTR(-ENOSPC);
+	}
 
 	/* with parallel dir operations all appends
 	* have to be serialized -bzzz */
