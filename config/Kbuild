# SPDX-License-Identifier: GPL-2.0

#
# This file is part of Lustre, http://www.lustre.org/
#
# There are four ways this Makefile can be called:
#
# 1.  As a subdirectory from the toplevel, for automake
# 2.  A wrapper around the kernel's makefile when building modules, to
#     possibly override the .config file
# 3.  At configure time, as the toplevel module dir for building
#     kernel tests
#

ifeq ($(PATCHLEVEL),)

ifeq ($(LUSTRE_LINUX_CONFIG),)

# case #1
include autoMakefile

else

# case #2
# Note that this comes from make -C $LINUX -f $LUSTRE/build/Makefile
# so "include Makefile" below includes $LINUX/Makefile, not this file
include $(LUSTRE_LINUX_CONFIG)
include Makefile
ifeq ($(DEQUOTE_CC_VERSION_TEXT),yes)
CONFIG_CC_VERSION_TEXT=$(shell echo ${CONFIG_CC_VERSION_TEXT:1:-1})
endif

endif # LUSTRE_LINUX_CONFIG

else # PATCHLEVEL
ifeq ($(DEQUOTE_CC_VERSION_TEXT),yes)
CONFIG_CC_VERSION_TEXT=$(shell echo ${CONFIG_CC_VERSION_TEXT:1:-1})
endif

# case #3
ifneq ($(LUSTRE_KERNEL_TEST),)
# extra-y works prior to 5.10
extra-y = $(LUSTRE_KERNEL_TEST)
# always-y works since 5.6
always-y = $(LUSTRE_KERNEL_TEST)
# always works for SUSE SP3 LTSS kernel
always = $(LUSTRE_KERNEL_TEST)
endif

obj-m := conftest.o

endif # PATCHLEVEL
