# SPDX-License-Identifier: GPL-2.0

#
# This file is part of Lustre, http://www.lustre.org/
#
# build/Rules.in
#
# Rules file for the kernel-related autoconf tests.
#

ifeq ($(PATCHLEVEL),)

include autoMakefile

fix-kext-ownership:
	@if test -d $(DESTDIR)$(kextdir) ; then \
		echo chown -R root:wheel $(DESTDIR)$(kextdir) ; \
		chown -R root:wheel $(DESTDIR)$(kextdir) || \
			echo >&2 "*** WARNING: Could not fix kext ownership for $(DESTDIR)$(kextdir)" ; \
	fi

else

include @LINUX_CONFIG@

EXTRA_CFLAGS := $(EXTRA_PRE_CFLAGS)
EXTRA_CFLAGS += @EXTRA_KCFLAGS@ @CFLAGS@
EXTRA_CFLAGS += $(EXTRA_POST_CFLAGS)

override LINUXINCLUDE += $(EXTRA_CFLAGS)

KBUILD_EXTRA_SYMBOLS += @EXTRA_SYMBOLS@

obj-m += $(patsubst %,%.o,$(MODULES))

endif # PATCHLEVEL
