#!/bin/bash
# SPDX-License-Identifier: NOASSERTION

#
# This file is part of Lustre, http://www.lustre.org/
#
# debian/lustre-client-module-dkms.prerm
#
# Script run before Lustre DKMS modules are removed
#

set -e

# Get the package version
package="lustre-client-modules"
version="$(dpkg-query -W -f='${Version}' "${package}-dkms" | sed -e 's/-[^-]*$//')"

dkms remove -m "${package}" -v "${version}" --all || true
