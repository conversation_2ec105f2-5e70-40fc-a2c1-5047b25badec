Source: lustre
Section: admin
Priority: optional
Maintainer: Lustre Developers <<EMAIL>>
Uploaders: Lustre Developers <<EMAIL>>
Standards-Version: 3.7.2
Build-Depends: debhelper (>= 11), bzip2

Package: lustre-client-modules-_KVERS_
Architecture: any
Recommends: linux-image, lustre-client-utils
Provides: lustre-client-modules
Description: Lustre Linux kernel module (kernel _KVERS_)
 This package contains the lustre loadable kernel modules for the
 patchless client for the Lustre cluster filesystem.
 .
 These modules are compiled for the _KVERS_ linux kernel.

Package: lustre-server-modules-_KVERS_
Architecture: any
Recommends: linux-image, lustre-server-utils
Provides: lustre-server-modules, lustre-client-modules
Conflicts: lustre-client-modules-_KVERS_
Replaces: lustre-client-modules-_KVERS_
Description: Lustre Server Linux kernel module (kernel _KVERS_)
 This package contains the loadable kernel modules for the
 ldiskfs filesystem as used by the Lustre cluster filesystem servers.
 .
 Note that you will only need this when you're running the
 node as a Lustre server. The package might also contain the ldiskfs
 module for use as a backing file system, in case it was enabled.
 In that case, you should also run a lustre-patched Kernel
 to achieve optimum performance. If you plan to run this with ZFS
 enabled, no kernel patches are needed at all.
 .
 These server modules provide and supersede the client modules, if they
 are compiled against same Kernel version.
 .
 These modules are compiled for the _KVERS_ linux kernel.
