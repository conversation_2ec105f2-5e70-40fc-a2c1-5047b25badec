#!/bin/bash
# SPDX-License-Identifier: NOASSERTION

#
# This file is part of Lustre, http://www.lustre.org/
#
# debian/lustre-client-module-dkms.postinst
#
# Script run after Lustre DKMS modules are installed
#

set -e

# Get the package version
package="lustre-client-modules"
version="$(dpkg-query -W -f='${Version}' "${package}-dkms" | sed -e 's/-[^-]*$//')"

dkms add -m "${package}" -v "${version}"
dkms build -m "${package}" -v "${version}" && dkms install -m "${package}" -v "${version}" --force || true
