#!/usr/bin/make -f
# SPDX-License-Identifier: NOASSERTION

#
# This file is part of Lustre, http://www.lustre.org/
#
# debian/rules
#
# Debian rules file
#
# Copying and modification is unlimited, provided that the modified
# version is marked as being modified.
# (This version has been modified from the original)
#
# Created 2009 by <PERSON> based on work by
# <PERSON><PERSON><PERSON>, <<EMAIL>>, <<EMAIL>>
# <PERSON>, and the pkg-lustre Debian maintainers
#

# Uncomment this to turn on verbose mode.
# export DH_VERBOSE=1

DEB_BUILD_GNU_SYSTEM = $(shell dpkg-architecture -qDEB_BUILD_GNU_SYSTEM)
DEB_BUILD_GNU_CPU = $(shell dpkg-architecture -qDEB_BUILD_GNU_CPU)


# whether to use module-assistant to build the kernel modules or not
USE_MA = true

# Whether to build the DKMS packages
BUILD_DKMS ?= false

ifeq ($(BUILD_DKMS),true)
	DKMS_TARGET = binary-$(DKMS_PKG)
else
	DKMS_TARGET =
endif

CFLAGS = -Wall -g
ifneq (,$(findstring noopt,$(DEB_BUILD_OPTIONS)))
        CFLAGS += -O0
else
        CFLAGS += -O2
endif

# This is the debhelper compatibility version to use.
export DH_COMPAT=11

# Module-assistant stuff
ifneq (,$(findstring server,$(DEB_BUILD_PROFILES)))
	PACKAGE=lustre-server-modules
else
	PACKAGE=lustre-client-modules
endif
MA_DIR ?= /usr/share/modass
-include $(MA_DIR)/include/generic.make
-include $(MA_DIR)/include/common-rules.make

ARCH=$(shell dpkg --print-architecture)
TOP_DIR:=$(shell pwd)

# This is the Lustre version
VERSION=$(shell sed -n -e '1s/.*(\(.*\)) .*/\1/p' debian/changelog)
UPVERSION=$(shell echo $(VERSION) | sed -e 's/-[^-]*$$//')

# KVER is set to LINUXRELEASE being by autoconf which is either
# $(uname -r) or the version of the source specified by the user
KVER?=$(LINUXRELEASE)

# KVERS, KSRC are the kernel version and source to use when building lustre
# Will be the default when building lustre-{server,client}-utils, etc but whatever module-assistant
# supplied when building modules
KVERS?=$(KVER)
KSRC?=$(LINUX_OBJ)
KSRC_TREE?=$(LINUX)
EXTRA_OPTIONS?=$(IB_OPTIONS)

ifneq ($(SYSTEMD_OPTIONS),)
	EXTRA_OPTIONS += $(SYSTEMD_OPTIONS)
endif

# Packages provided for both client and server builds
DEV_PKG=lustre-dev
SOURCE_PKG=lustre-source

ifneq (,$(findstring notests,$(DEB_BUILD_PROFILES)))
	TESTS_PKG=
	BINARY_TESTS_PKG=
else
	TESTS_PKG=lustre-tests
	BINARY_TESTS_PKG=binary-$(TESTS_PKG)
endif

ifneq (,$(findstring noiokit,$(DEB_BUILD_PROFILES)))
	IOKIT_PKG=
	BINARY_IOKIT_PKG=
else
	IOKIT_PKG=lustre-iokit
	BINARY_IOKIT_PKG=binary-$(IOKIT_PKG)
endif

# Packages that are only built for server OR client builds
# The difference is that server build contain more modules & utils
ifneq (,$(findstring server,$(DEB_BUILD_PROFILES)))
ifneq (,$(findstring noutils,$(DEB_BUILD_PROFILES)))
	UTILS_PKG=
	BINARY_UTILS_PKG=
else
	UTILS_PKG=lustre-server-utils
	BINARY_UTILS_PKG=binary-$(UTILS_PKG)
endif
	MODS_PKG=lustre-server-modules
	RESOURCE_PKG=lustre-resource-agents
	RESOURCE_TARGET=binary-$(RESOURCE_PKG)
	DKMS_PKG=
else
ifneq (,$(findstring noutils,$(DEB_BUILD_PROFILES)))
	UTILS_PKG=
	BINARY_UTILS_PKG=
else
	UTILS_PKG=lustre-client-utils
	BINARY_UTILS_PKG=binary-$(UTILS_PKG)
endif
	MODS_PKG=lustre-client-modules
	RESOURCE_TARGET=
	RESOURCE_PKG=
	DKMS_PKG=lustre-client-modules-dkms
endif

#Build dir
#BUILDDIR=debian/build
#SRCDIR=../..
# I'm afraid we seem to have broken the BUILDDIR concept in Lustre's
# autoconf macros
BUILDDIR=.
SRCDIR=.

AVAIL_CPUS := $(shell getconf _NPROCESSORS_ONLN 2>/dev/null || echo 1)
ifneq ($(AVAIL_CPUS),1)
      PMAKEARGS := -j $(AVAIL_CPUS)
endif

autogen: autogen-stamp
autogen-stamp: patch-stamp
	# see if any patches requires us to run autogen
	# (for a distribution release tarball, it is expected that if
	#  downstream adds any patches that requires autogen.sh to be
	#  run, a patch will be added by downstream to install the
	#  needed autogen.sh scripts
	#  see https://bugzilla.lustre.org/attachment.cgi?id=27156
	#  for an example)
	if grep -e "^--- .*\/autoconf\/" \
		-e "^--- .*\/autoMakefile\.am" \
		-e "^--- .*\/Makefile\.am" \
		-e "^--- .*\/configure\.ac" \
		-e "^--- .*\/configure\.in" \
	    debian/patches/*; then \
		if [ ! -f build/autogen.sh ]; then \
			echo "You have patches which require autogen.sh to be run, but it doesn't exist"; \
			echo "Please see https://bugzilla.lustre.org/attachment.cgi?id=27156"; \
			exit 1; \
		fi; \
		cp /usr/share/misc/config.sub config.sub; \
		cp /usr/share/misc/config.sub ldiskfs/config.sub; \
		cp /usr/share/misc/config.guess config.guess; \
		cp /usr/share/misc/config.guess ldiskfs/config.guess; \
		bash build/autogen.sh; \
	fi; \
	touch $@

# This section configures the lustre-utilities packages.
#
# Both client and server builds are quite similar, as the utilities build
# must create the modules too, to produce the "osd_<fstype>.so" libraries.
#
# The main difference is that the utilities build does not archive the
# modules, whereas the modules build omits the utilities, to create two
# neatly separated debian files.
#
# Note: KERNEL_SRC, KERNEL_OBJ, ZFS_SRC and SPL_SRC need to be set from the
# outside. This is done by "make debs". As such, invoking "debuild" or such
# direct debian build tools will lead to a client-only build.
configure: configure-stamp
configure-stamp: autogen-stamp debian/control.main debian/control.modules.in
	dh_testdir
	# touch files to same date, to avoid auto*
	find . -type f -print0 | xargs -0 touch -r COPYING; \
	if [ "$(BUILDDIR)" != "." ]; then \
		mkdir -p $(BUILDDIR)/build $(BUILDDIR)/lustre/contrib; \
		cp build/Makefile $(BUILDDIR)/build/; \
	fi
	# Determine flags that are different between server/client module builds
	echo "Enabled Build Profiles: $${DEB_BUILD_PROFILES}"
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "server"; then \
		export EXTRAFLAGS="--enable-server"; \
		if echo "$${DEB_BUILD_PROFILES}" | grep -q "systemd"; then \
			systemd_dir=$$(echo $${SYSTEMD_OPTIONS} | \
				       sed -e "s/--with-systemdsystemunitdir=\///g" \
					   -e "s/\//\\\\\//g"); \
			sed "s/lib\/systemd\/system/$${systemd_dir}/g" \
				debian/lustre-server-utils.install.in \
				> debian/lustre-server-utils.install; \
		else \
			sed /systemd/d debian/lustre-server-utils.install.in \
				> debian/lustre-server-utils.install; \
		fi; \
	else \
		export EXTRAFLAGS="--disable-server"; \
		if echo "$${DEB_BUILD_PROFILES}" | grep -q "systemd"; then \
			systemd_dir=$$(echo $${SYSTEMD_OPTIONS} | \
				       sed -e "s/--with-systemdsystemunitdir=\///g" \
					   -e "s/\//\\\\\//g"); \
			sed "s/lib\/systemd\/system/$${systemd_dir}/g" \
				debian/lustre-client-utils.install.in \
				> debian/lustre-client-utils.install; \
		else \
			sed /systemd/d debian/lustre-client-utils.install.in \
				> debian/lustre-client-utils.install; \
		fi; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "nompi"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --disable-mpitests" ; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "notests"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --disable-tests"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "noutils"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --disable-utils"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "noiokit"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --disable-iokit"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "zfs"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --with-zfs=$${ZFS_SRC} --with-spl=$${SPL_SRC}"; \
	else \
		export EXTRAFLAGS="$${EXTRAFLAGS} --without-zfs" ; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "ldiskfs"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} \
			--enable-ldiskfs --enable-quilt"; \
	else \
		export EXTRAFLAGS="$${EXTRAFLAGS} --disable-ldiskfs"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "multiple-lnds"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --enable-multiple-lnds"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "ext_o2ib"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --with-o2ib=$${O2IB_SRC}"; \
	elif echo "$${DEB_BUILD_PROFILES}" | grep -q "int_o2ib"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --with-o2ib=yes"; \
	else \
		export EXTRAFLAGS="$${EXTRAFLAGS} --with-o2ib=no"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "gnilnd"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --enable-gni"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "kfilnd"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --with-kfi=/usr/src/kfabric"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "efalnd"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --enable-efa"; \
	fi; \
	options="gss crypto pinger checksum flock lru-resize"; \
	options="$${options} mindf fail-alloc invariants lu_ref pgstate-track"; \
	options="$${options} libcfs-cdebug libcfs-trace libcfs-assert"; \
	options="$${options} panic_dumplog readline libpthread"; \
	options="$${options} backoff gni efence"; \
	for option in $${options}; do \
		if echo "$${DEB_BUILD_PROFILES}" | grep -qw "$${option}"; then \
			export EXTRAFLAGS="$${EXTRAFLAGS} --enable-$${option}"; \
		elif echo "$${DEB_BUILD_PROFILES}" | grep -qw "no$${option}"; then \
			export EXTRAFLAGS="$${EXTRAFLAGS} --disable-$${option}"; \
		fi; \
	done; \
	if [ -f "$${CONFIG_CACHE_FILE}" ]; then \
		export TMP_CACHE_FILE=$$(mktemp); \
		sed -e "/ac_cv_env/d" \
		    -e "/am_cv_CC_dependencies_compiler_type/d" \
		    -e "/lb_cv_mpi_tests/d" \
		       "$${CONFIG_CACHE_FILE}" > $${TMP_CACHE_FILE}; \
		export EXTRAFLAGS="$${EXTRAFLAGS} --cache-file=$${TMP_CACHE_FILE}"; \
	fi; \
	echo "Final value of EXTRAFLAGS: $${EXTRAFLAGS}"; \
	( cd $(BUILDDIR) && \
		$(SRCDIR)/configure --disable-dependency-tracking \
			--with-linux=$${KERNEL_SRC} \
			--with-linux-obj=$${KERNEL_OBJ} \
			--enable-quota \
			--with-kmp-moddir=updates/kernel \
			$${EXTRAFLAGS} \
			$(EXTRA_OPTIONS) \
	); \
	touch $@

build-arch build-indep: build

build: build-stamp
build-stamp: patch-stamp configure-stamp
	dh_testdir
	$(MAKE) -C $(BUILDDIR) $(PMAKEARGS)
	$(MAKE) -C $(BUILDDIR) DESTDIR=$(TOP_DIR)/debian/tmp install
	# jump our lustre-[client|server]-modules into the control file if not using m-a
	if ! $(USE_MA); then \
		(cat debian/control.main; sed -e '1,/^$$/d' -e "s/_KVERS_/$(KVER)/g" < debian/control.modules.in) > debian/control; \
		for file in debian/*_KVERS_*; do \
			newfile=$$(echo $$file | sed -e "s/_KVERS_/$(KVER)/g"); \
			cp $$file $$newfile; \
		done; \
	fi
	touch $@

# let's try and operate as close as the user would and not build modules
# here (for now)
# instead we will build the modules with m-a in the "make debs" target
# binary-arch: binary-$(UTILS_PKG) binary-$(TESTS_PKG) binary-$(DEV_PKG) \
# binary-$(MODS_PKG) binary-kern-mods
binary-arch: binary-$(DEV_PKG) \
	     $(BINARY_UTILS_PKG) $(BINARY_IOKIT_PKG) $(BINARY_TESTS_PKG) \
	     $(RESOURCE_TARGET)

binary-indep: binary-$(SOURCE_PKG) $(DKMS_TARGET)

binary: binary-indep binary-arch

binary-$(UTILS_PKG): build-stamp
	dh_testdir
	dh_testroot
	dh_installdirs -p $(UTILS_PKG)
	dh_installdocs -p  $(UTILS_PKG)
	dh_installman -p $(UTILS_PKG)
	dh_install -p $(UTILS_PKG)
	rm -rf debian/$(UTILS_PKG)/usr/bin/iokit-* \
	       debian/$(UTILS_PKG)/usr/bin/*-survey \
	       debian/$(UTILS_PKG)/usr/bin/mcreate \
	       debian/$(UTILS_PKG)/usr/bin/statx \
	       debian/$(UTILS_PKG)/usr/sbin/wirecheck \
	       debian/$(UTILS_PKG)/usr/sbin/wiretest
	dh_makeshlibs -p $(UTILS_PKG)
	dh_installexamples -p $(UTILS_PKG)
	dh_installchangelogs -p $(UTILS_PKG) lustre/ChangeLog
	dh_compress -p $(UTILS_PKG)
	dh_strip -p $(UTILS_PKG)
	dh_installdeb -p $(UTILS_PKG)
	dh_fixperms -p $(UTILS_PKG)
	dh_gencontrol -p $(UTILS_PKG)
	dh_md5sums -p $(UTILS_PKG)
	dh_builddeb -p $(UTILS_PKG)

binary-$(RESOURCE_PKG): build-stamp
	dh_testdir
	dh_testroot
	dh_installdirs -p $(RESOURCE_PKG)
	dh_installdocs -p  $(RESOURCE_PKG)
	dh_installman -p $(RESOURCE_PKG)
	dh_install -p $(RESOURCE_PKG)
	dh_makeshlibs -p $(RESOURCE_PKG)
	dh_installexamples -p $(RESOURCE_PKG)
	dh_installchangelogs -p $(RESOURCE_PKG) lustre/ChangeLog
	dh_compress -p $(RESOURCE_PKG)
	dh_strip -p $(RESOURCE_PKG)
	dh_installdeb -p $(RESOURCE_PKG)
	dh_fixperms -p $(RESOURCE_PKG)
	dh_gencontrol -p $(RESOURCE_PKG)
	dh_md5sums -p $(RESOURCE_PKG)
	dh_builddeb -p $(RESOURCE_PKG)

binary-$(IOKIT_PKG): build-stamp
	dh_testdir
	dh_testroot
	dh_installdirs -p $(IOKIT_PKG)
	dh_installdocs -p  $(IOKIT_PKG)
	dh_installman -p $(IOKIT_PKG)
	dh_install -p $(IOKIT_PKG)
	rm -rf debian/$(IOKIT_PKG)/usr/bin/lustre_req_history \
	       debian/$(IOKIT_PKG)/usr/bin/*ll*stat \
	       debian/$(IOKIT_PKG)/usr/bin/lfs-*
	dh_makeshlibs -p $(IOKIT_PKG)
	dh_installexamples -p $(IOKIT_PKG)
	dh_installchangelogs -p $(IOKIT_PKG) lustre/ChangeLog
	dh_compress -p $(IOKIT_PKG)
	dh_strip -p $(IOKIT_PKG)
	dh_installdeb -p $(IOKIT_PKG)
	dh_fixperms -p $(IOKIT_PKG)
	dh_gencontrol -p $(IOKIT_PKG)
	dh_md5sums -p $(IOKIT_PKG)
	dh_builddeb -p $(IOKIT_PKG)

binary-$(TESTS_PKG): build-stamp
	dh_testdir
	dh_testroot
	dh_installdirs -p $(TESTS_PKG)
	dh_installdocs -p  $(TESTS_PKG)
	dh_installman -p $(TESTS_PKG)
	dh_install -p $(TESTS_PKG)
	dh_makeshlibs -p $(TESTS_PKG)
	dh_installexamples -p $(TESTS_PKG)
	dh_installchangelogs -p $(TESTS_PKG) lustre/ChangeLog
	dh_compress -p $(TESTS_PKG)
	dh_strip -p $(TESTS_PKG)
	dh_installdeb -p $(TESTS_PKG)
	dh_fixperms -p $(TESTS_PKG)
	dh_gencontrol -p $(TESTS_PKG)
	dh_md5sums -p $(TESTS_PKG)
	dh_builddeb -p $(TESTS_PKG)

binary-$(DEV_PKG): build-stamp
	dh_testdir
	dh_testroot
	dh_installdirs -p $(DEV_PKG)
	dh_install -p $(DEV_PKG)
	dh_installdocs -p $(DEV_PKG)
	dh_installchangelogs -p $(DEV_PKG) lustre/ChangeLog
	dh_compress -p $(DEV_PKG)
	dh_installdeb -p $(DEV_PKG)
	dh_fixperms -p $(DEV_PKG)
	dh_gencontrol -p $(DEV_PKG)
	dh_md5sums -p $(DEV_PKG)
	dh_builddeb -p $(DEV_PKG)

binary-$(SOURCE_PKG): build-stamp
	dh_testdir 
	dh_testroot
	# Copy everything 
	dh_installdirs -p $(SOURCE_PKG) usr/src/modules/lustre/debian
	rsync -av --exclude='debian/$(SOURCE_PKG)/*' \
	      --link-dest=$(PWD) \
	      . debian/$(SOURCE_PKG)/usr/src/modules/lustre
	# Remove FULLCLEAN setting
	rm debian/$(SOURCE_PKG)/usr/src/modules/lustre/debian/rules
	cp -v debian/rules debian/$(SOURCE_PKG)/usr/src/modules/lustre/debian/rules
	sed -i -e 's/FULLCLEAN=full-clean/FULLCLEAN=/' debian/$(SOURCE_PKG)/usr/src/modules/lustre/debian/rules
	# clean up dest
	rm -rf debian/$(SOURCE_PKG)/usr/src/modules/lustre/debian/patched \
	       debian/$(SOURCE_PKG)/usr/src/modules/lustre/debian/patches/* 
	$(MAKE) -C debian/$(SOURCE_PKG)/usr/src/modules/lustre -f debian/rules clean
	# Create the module-source tarball.
	cd debian/$(SOURCE_PKG)/usr/src && tar --exclude-vcs -jcf lustre-$${SRC_GIT_HASH}.tar.bz2 modules
	rm -rf debian/$(SOURCE_PKG)/usr/src/modules
	dh_install -p$(SOURCE_PKG)
	dh_installchangelogs -p $(SOURCE_PKG) lustre/ChangeLog
	dh_installdocs -p $(SOURCE_PKG) 
	dh_link -p $(SOURCE_PKG) /usr/share/modass/packages/default.sh /usr/share/modass/overrides/lustre-source
	dh_compress -p $(SOURCE_PKG)
	dh_installdeb -p $(SOURCE_PKG)
	dh_fixperms -p $(SOURCE_PKG)
	dh_gencontrol -p $(SOURCE_PKG)
	dh_md5sums -p $(SOURCE_PKG)
	dh_builddeb -p $(SOURCE_PKG)

binary-$(DKMS_PKG): build-stamp
	dh_testdir
	dh_testroot
	# Create /usr/src/lustre-client-modules-<version>
	dh_installdirs -p $(DKMS_PKG) usr/src/lustre-client-modules-$(UPVERSION)
	# Copy the source
	rsync -av --exclude='debian/$(DKMS_PKG)/*' --exclude='.git/' \
		--link-dest=$(PWD) \
		. debian/$(DKMS_PKG)/usr/src/lustre-client-modules-$(UPVERSION)
	# Clean it up
	$(MAKE) -C debian/$(DKMS_PKG)/usr/src/lustre-client-modules-$(UPVERSION) -f debian/rules clean

	$(MAKE) install -C lustre/include DESTDIR=$(CURDIR)/debian/$(DKMS_PKG)
	$(MAKE) install -C lnet/include DESTDIR=$(CURDIR)/debian/$(DKMS_PKG)
	rm -rf $(CURDIR)/debian/$(DKMS_PKG)/usr/include/lustre

	# Put the DKMS configuration file in place
	sed -e 's/[@]UPVERSION[@]/$(UPVERSION)/' debian/dkms.conf.in > debian/$(DKMS_PKG)/usr/src/lustre-client-modules-$(UPVERSION)/dkms.conf
	dh_install -p $(DKMS_PKG)
	dh_installchangelogs -p $(DKMS_PKG) lustre/ChangeLog
	dh_installdocs -p $(DKMS_PKG)
	dh_compress -p $(DKMS_PKG)
	dh_installdeb -p $(DKMS_PKG)
	dh_strip -p $(DKMS_PKG)
	dh_fixperms -p $(DKMS_PKG)
	dh_gencontrol -p $(DKMS_PKG)
	dh_md5sums -p $(DKMS_PKG)
	dh_builddeb -p $(DKMS_PKG)

# if only we could use m-a for this, but this stupid "compliant.list"
# thing fouls that up
binary-kern-mods:
	# Build client or server modules
	mkdir -p debian/m-a_root/usr_src/modules
	ln -s ../../../../ debian/m-a_root/usr_src/modules/lustre
	CC=$(CC) RELAX_CC_CHECK=$(CC) \
	m-a -t -u debian/m-a_root/ -d -v -k $(KSRC) build lustre
	mv -f debian/m-a_root/*.deb ..

# an alternative (to module-assistant) method of building the kernel modules
binary-$(MODS_PKG): build-stamp
	if ! $(USE_MA); then \
		cp debian/$(MODS_PKG).install debian/$(MODS_PKG)-$(KVER).install ; \
		dh_testdir; \
		dh_testroot; \
		dh_installdirs -p $(MODS_PKG)-$(KVER); \
		dh_installdocs -p $(MODS_PKG)-$(KVER); \
		dh_install -p $(MODS_PKG)-$(KVER); \
		dh_makeshlibs -p $(MODS_PKG)-$(KVER); \
		dh_installchangelogs -p $(MODS_PKG)-$(KVER) lustre/ChangeLog; \
		dh_compress -p $(MODS_PKG)-$(KVER); \
		dh_installdeb -p $(MODS_PKG)-$(KVER); \
		dh_strip -p $(MODS_PKG)-$(KVER); \
		dh_fixperms -p $(MODS_PKG)-$(KVER); \
		dh_gencontrol -p $(MODS_PKG)-$(KVER); \
		dh_md5sums -p $(MODS_PKG)-$(KVER); \
		dh_builddeb -p $(MODS_PKG)-$(KVER); \
	fi


###
### For module-assistant
###

# the kdist_clean target is called by make-kpkg modules_clean and from
# kdist* rules. It is responsible for cleaning up any changes that have
# been made by the other kdist_commands (except for the .deb files created)

kdist_clean: 
	# can't dh_clean here or we remove the build artifacts for the other packages
	# still in progress
	#dh_clean
	#-$(MAKE) $(MFLAGS) -f debian/rules clean
	# put the userspace tools' control file back
	cp debian/control.main debian/control


# module assistant calculates all needed things for us and sets
# # following variables:
# # KSRC (kernel source directory), KVERS (kernel version string), KDREV
# # (revision of the Debian kernel-image package), CC (the correct
# # compiler), VERSION (the final package version string), PKGNAME (full
# # package name with KVERS included), DEB_DESTDIR (path to store DEBs)
#
# # The kdist_configure target is called by make-kpkg modules_config and
# # by kdist* rules by dependency. It should configure the module so it is
# # ready for compilation (mostly useful for calling configure).
# # prep-deb-files from module-assistant creates the neccessary debian/ 
kdist_config: prep-deb-files patch-stamp
	#  Check build dependencies again, needs to be done since we just
	# replaced the control file.
	dpkg-checkbuilddeps
	# Prepare the kernel to lustres' liking
	# as non-root, we won't have permission for this
	#-$(MAKE) -C $(KSRC) prepare scripts
	# touch files to same date, to avoid auto*
	find . -type f -print0 | xargs -0 touch -r COPYING \;
	# Determine flags that are different between server/client module builds
	# Note: It doesn't seem possible to *only* build modules.
	echo "Enabled Build Profiles: $${DEB_BUILD_PROFILES}"
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "server"; then \
		export EXTRAFLAGS="--enable-server"; \
	else \
		export EXTRAFLAGS="--disable-server"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "nompi"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --disable-mpitests" ; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "notests"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --disable-tests"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "noutils"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --disable-utils"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "noiokit"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --disable-iokit"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "zfs"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --with-zfs=$${ZFS_SRC} --with-spl=$${SPL_SRC}"; \
	else \
		export EXTRAFLAGS="$${EXTRAFLAGS} --without-zfs" ; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "ldiskfs"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} \
			--enable-ldiskfs --enable-quilt"; \
	else \
		export EXTRAFLAGS="$${EXTRAFLAGS} \
			--disable-ldiskfs --disable-quilt"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "multiple-lnds"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --enable-multiple-lnds"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "ext_o2ib"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --with-o2ib=$${O2IB_SRC}"; \
	elif echo "$${DEB_BUILD_PROFILES}" | grep -q "int_o2ib"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --with-o2ib=yes"; \
	else \
		export EXTRAFLAGS="$${EXTRAFLAGS} --with-o2ib=no"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "gnilnd"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --enable-gni"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "kfilnd"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --with-kfi=/usr/src/kfabric"; \
	fi; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "efalnd"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --enable-efa"; \
	fi; \
	options="gss crypto pinger checksum flock lru-resize"; \
	options="$${options} mindf fail-alloc invariants lu_ref pgstate-track"; \
	options="$${options} libcfs-cdebug libcfs-trace libcfs-assert"; \
	options="$${options} panic_dumplog readline libpthread"; \
	options="$${options} backoff gni efence"; \
	for option in $${options}; do \
		if echo "$${DEB_BUILD_PROFILES}" | grep -qw "$${option}"; then \
			export EXTRAFLAGS="$${EXTRAFLAGS} --enable-$${option}"; \
		elif echo "$${DEB_BUILD_PROFILES}" | grep -qw "no$${option}"; then \
			export EXTRAFLAGS="$${EXTRAFLAGS} --disable-$${option}"; \
		fi; \
	done; \
	if echo "$${DEB_BUILD_PROFILES}" | grep -q "notests"; then \
		export EXTRAFLAGS="$${EXTRAFLAGS} --disable-tests"; \
	fi; \
	if [ -f "$${CONFIG_CACHE_FILE}" ]; then \
		export TMP_CACHE_FILE=$$(mktemp --tmpdir newconfig-XXXXXXXX.cache); \
		sed -e "/ac_cv_env/d" \
		    -e "/am_cv_CC_dependencies_compiler_type/d" \
		    -e "/lb_cv_mpi_tests/d" \
		       "$${CONFIG_CACHE_FILE}" > $${TMP_CACHE_FILE}; \
		export EXTRAFLAGS="$${EXTRAFLAGS} --cache-file=$${TMP_CACHE_FILE}"; \
	fi; \
	echo "Final value of EXTRAFLAGS: $${EXTRAFLAGS}"; \
	./configure --with-linux=$(KSRC_TREE) \
		--with-linux-obj=$(KSRC) \
		--disable-dependency-tracking \
		--disable-doc  \
		--disable-iokit \
		--enable-quota \
		--with-kmp-moddir=updates/kernel \
		$${EXTRAFLAGS} \
		$(EXTRA_OPTIONS)


kdist_configure: kdist_config

# the binary-modules rule is invoked by module-assistant while processing the
# kdist* targets. It is called by module-assistant or make-kpkg and *not*
# during a normal build
binary-modules: prep-deb-files
	dh_testroot
	dh_prep
	$(MAKE) $(PMAKEARGS)
	$(MAKE) install DESTDIR=$(CURDIR)/debian/$(PKGNAME)
	# Remove stuff that doesn't belong (no module-only target)
	cd $(CURDIR)/debian/$(PKGNAME) && rm -rf usr/bin usr/sbin usr/lib usr/share usr/include/lustre lib/systemd sbin etc
	dh_installdocs -p$(PKGNAME)
	dh_installchangelogs -p$(PKGNAME)
	dh_installmodules -p $(PKGNAME)
	dh_compress -p $(PKGNAME)
	dh_fixperms -p $(PKGNAME)
	sed "s/depmod -a/depmod -a $(KVERS)/" $(CURDIR)/debian/postinst > $(CURDIR)/debian/$(PKGNAME).postinst
	sed "s/depmod -a/depmod -a $(KVERS)/" $(CURDIR)/debian/postrm > $(CURDIR)/debian/$(PKGNAME).postrm
	chmod +x $(CURDIR)/debian/$(PKGNAME).postinst $(CURDIR)/debian/$(PKGNAME).postrm
	dh_installdeb -p $(PKGNAME)
	dh_gencontrol -- -v$(VERSION)
	dh_md5sums -p $(PKGNAME)
	dh_builddeb --destdir=$(DEB_DESTDIR) -p $(PKGNAME)
	dh_prep -p $(PKGNAME)

clean:
	dh_testdir
	dh_testroot
	-$(MAKE) distclean
	rm -rf  debian/substvars debian/*.bak debian/*~ *~  *-stamp
	ls -d debian/lustre-*-modules-* | grep -v _KVERS_ | grep -v dkms | xargs rm -f || true
	# only remove this if the clean was not called from kdist_clean
	if [ "$$MA_SOURCE_PKG" = "" ]; then \
		rm -rf  debian/m-a_root; \
	fi
	dh_clean
	# make sure we have at least a basic control in place (see build-stamp)
	rm -f debian/control
	cp debian/control.main debian/control

patch: patch-stamp
patch-stamp:
	touch $@

unpatch:
	rm -rf debian/patched patch-stamp

.PHONY: binary-arch clean source diff  binary binary-indep binary-arch install configure
