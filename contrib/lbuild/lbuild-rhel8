# vim:expandtab:shiftwidth=4:softtabstop=4:tabstop=4:

source ${LBUILD_DIR}/lbuild-rhel

# increment this if you have made a change that should force a new kernel
# to build built
BUILD_GEN+=".0"

SPEC_NAME="kernel.spec"
DEVEL_PATH_ARCH_DELIMETER="."
USE_KABI=true

# force local definition of %dist into ~/.rpmmacros
# to avoid verbose extended strings like ".el8.centos"
# in kernel version and rpm names
#
RMAC=$HOME/.rpmmacros
grep '^%dist' $RMAC &> /dev/null || echo '%dist .el8' >> $RMAC

# We modify the RHEL8 kmodtool script, because it expects
# the kernel source to be in the correct packaged RHEL location.
# lbuild does not put the source in correct packaged RHEL location.
RHEL_KMODTOOL=/usr/lib/rpm/redhat/kmodtool
LBUILD_KMODTOOL="${LBUILD_DIR}/rhel8/kmodtool"
cp  $RHEL_KMODTOOL $LBUILD_KMODTOOL
pushd ${LBUILD_DIR}/rhel8
# Replace the hardcoded /usr/src/kernels with our build path
tmp="${TOPDIR}/reused/usr/src/kernels"
tmp="${tmp//\//\\/}"
sed -i "s/\/usr\/src\/kernels/${tmp}/g" ${LBUILD_DIR}/rhel8/kmodtool
chmod 755 kmodtool
popd
# Next we replace %kernel_module_package with one that is nearly
# identical, but calls lbuild's modified kmodtool script.
# We need to cleanout the previous entry
sed -i "/^%kernel_module_package/,/^)}$/d" $RMAC
sed -e "s|REPLACE_ME|$LBUILD_KMODTOOL|" -e "s/\/usr\/src\/kernels/${tmp}/g" \
    ${LBUILD_DIR}/rhel8/rpmmacros.template > ${LBUILD_DIR}/rhel8/rpmmacros
cat ${LBUILD_DIR}/rhel8/rpmmacros >> $RMAC

patch_spec_rhel8() {
    sed -i -e '/Provides: kernel-uname-r =/a\
Provides: kernel-lustre = %{KVRA}%{?1:.%{1}}\\' \
           -e '/Provides: kernel-devel-uname-r =/a\
Provides: kernel-devel-lustre = %{KVRA}%{?1:.%{1}}\\' \
           -e '/^    find $RPM_BUILD_ROOT\/lib\/modules\/$KernelVer/a\
    cp -a fs/ext4/* $RPM_BUILD_ROOT/lib/modules/$KernelVer/build/fs/ext4\
    rm -f $RPM_BUILD_ROOT/lib/modules/$KernelVer/build/fs/ext4/ext4-inode-test*' \
          SPECS/$SPEC_NAME 2>&1 ||
        fatal 1 "Error while editing SPECS/$SPEC_NAME"
}

patch_spec() {
    local buildid="$1"

    # the el8 .spec file requires patch names to begin with "patch-3."
    # so rename the lustre patch from lbuild to one the .spec will like
    mv $TOPDIR/SOURCES/linux-${lnxmaj}-lustre.patch \
	$TOPDIR/SOURCES/patch-${lnxmaj}-lustre.patch

    # edit the SPEC with our changes
    patch_spec_rhel8 "$buildid"
    sed -i -e '/^# empty final patch to facilitate testing of kernel patches/i\
# adds Lustre patches\
Patch99995: patch-%{version}-lustre.patch' \
           -e '/^ApplyOptionalPatch linux-kernel-test.patch/i\
\
# lustre patch\
ApplyOptionalPatch patch-%{version}-lustre.patch\
' \
           -e '/^# Dynamically generate kernel/a echo "CONFIG_BH_LRU_SIZE=16" >> config-generic'\
          SPECS/$SPEC_NAME 2>&1 || \
        fatal 1 "Error while editing SPECS/$SPEC_NAME"


    return 0

}

unpack_linux_devel_rpm-rhel8() {
    local callers_rpm="$1"

    unpack_linux_devel_rpm-rhel "$callers_rpm"


    return 0

}

get_rpmbuildopts() {

    if $KERNEL_LUSTRE_NAMING; then
        echo -e "--define \"variant -lustre\" \c"
    fi
    echo "--with firmware --without debug"

    return 0

}

find_linux_rpm-rhel8() {
    local prefix="$1"
    local wanted_kernel="$2"
    local pathtorpms=${3:-"$KERNELRPMSBASE/$lnxmaj/$DISTROMAJ/$TARGET_ARCH"}

    find_linux_rpm-rhel "$prefix" "$wanted_kernel" "$pathtorpms"

}

kernel_srpm_location() {
	local kdir

	case $lnxrel in
		*8_5) kdir="8.5.2111" ;;
		*8_4) kdir="8.4.2105" ;;
		*8_3) kdir="8.3.2011" ;;
		*8_2) kdir="8.2.2004" ;;
		*8_1) kdir="8.1.1911" ;;
		*8_0) kdir="8.0.1905" ;;
		*) ;;
	esac

	echo "http://vault.centos.org/$kdir/BaseOS/Source/SPackages/"
}

kernel_debuginfo_location() {
	echo "http://debuginfo.centos.org/8/x86_64/Packages/"
}

cleanup_rpmmacros() {
	sed -i "/^%kernel_module_package/,/^)}$/d" $RMAC
}

apply_kmod_requires_conflicts() {
    if $PATCHLESS; then
        # don't allow the patched kernel to be considered as a valid kernel
        # for the patchless client
        echo "Conflicts:      kernel-lustre" >> rpm/kmp-lustre.preamble
    fi
}
