# The format of each line is:
# mistake||correction
#
ALWAYS_EXCEPT||always_except
alloca||malloc
cfs_get_random_bytes||get_random_bytes
cfs_hash_bd_t||struct cfs_hash_bd
cfs_hash_bucket_t||struct cfs_hash_bucket
cfs_hash_cond_arg_t||struct cfs_hash_cond_arg
cfs_hash_dhead_dep_t||struct cfs_hash_dhead_dep
cfs_hash_dhead_t||struct cfs_hash_dhead
cfs_hash_head_dep_t||struct cfs_hash_head_dep
cfs_hash_head_t||struct cfs_hash_head
cfs_hash_hlist_ops_t||struct cfs_hash_hlist_ops
cfs_hash_lock_ops_t||struct cfs_hash_lock_ops
cfs_hash_lock_t||union cfs_hash_lock
cfs_hash_lookup_intent_t||enum cfs_hash_lookup_intent
cfs_hash_ops_t||struct cfs_hash_ops
cfs_hash_t||struct cfs_hash
cfs_rcu_head_t||struct rcu_head
cfs_rand||get_random_u32
cfs_srand||add_device_randomness
cfs_size_round||round_up
cfs_trimwhite||strim
cfs_time_add_64||ktime_add
cfs_time_after||time_after
cfs_time_aftereq||time_after_eq
cfs_time_before||time_before
cfs_time_beforeeq||time_before_eq
cfs_time_beforeq_64||ktime_compare
cfs_time_before_64||ktime_before
cfs_time_current||jiffies
cfs_time_current_64||ktime_get
cfs_time_current_sec||ktime_get_real_seconds
ci_nob||ci_bytes
CLASSERT||BUILD_BUG_ON()
container_of0||container_of_safe
crw_count||crw_bytes
msecs_to_jiffies||cfs_time_seconds
DEFINE_TIMER||CFS_DEFINE_TIMER
define OBD_CONNECT[A-Z0-9_]*||see 'XXX README XXX' below <NAME_EMAIL>
__u[136][624] ocd_[a-z0-9_]*||see 'XXX README XXX' below <NAME_EMAIL>
del_timer||timer_delete
del_timer_sync||timer_delete_sync
del_singleshot_timer_sync||timer_delete_sync
\.\*s||DNAME and encode_fn_len()
d_name.name||encode_fn_xxx
DN_MAX_BONUSLEN||DN_BONUS_SIZE(dnodesize)
DN_OLD_MAX_BONUSLEN||DN_BONUS_SIZE(DNODE_MIN_SIZE)
ENOTSUPP||EOPNOTSUPP
ERR_PTR.PTR_ERR||ERR_CAST
EWOULDBLOCK||EAGAIN
fd_flags||lfd_file_flags
from_timer||cfs_from_timer
ft_nob||ft_bytes
f_dentry||f_path.dentry
[^_]get_seconds||ktime_get_real_seconds
HZ||cfs_time_seconds
it_flags||it_open_flags
kmem_cache_alloc.*GFP_ZERO||kmem_cache_zalloc
LCK_MINMODE||LCK_MODE_MIN
LCK_MAXMODE||LCK_MODE_END
LDEBUGFS_SEQ_FOPS_.*||create Documentation/man4/ page for this parameter
ldebugfs_remove||debugfs_remove_recursive
ldlm_appetite_t||enum ldlm_appetite
ldlm_cancel_flags_t||enum ldlm_cancel_flags
ldlm_clear_ast_discard_data||deprecated, use LDLM_FL_AST_DISCARD_DATA directly
ldlm_clear_ast_sent||deprecated, use LDLM_FL_AST_SENT directly
ldlm_clear_atomic_cb||deprecated, use LDLM_FL_ATOMIC_CB directly
ldlm_clear_bl_ast||deprecated, use LDLM_FL_BL_AST directly
ldlm_clear_bl_done||deprecated, use LDLM_FL_BL_DONE directly
ldlm_clear_block_conv||deprecated, use LDLM_FL_BLOCK_CONV directly
ldlm_clear_block_granted||deprecated, use LDLM_FL_BLOCK_GRANTED directly
ldlm_clear_block_nowait||deprecated, use LDLM_FL_BLOCK_NOWAIT directly
ldlm_clear_block_wait||deprecated, use LDLM_FL_BLOCK_WAIT directly
ldlm_clear_cancel||deprecated, use LDLM_FL_CANCEL directly
ldlm_clear_canceling||deprecated, use LDLM_FL_CANCELING directly
ldlm_clear_cancel_on_block||deprecated, use LDLM_FL_CANCEL_ON_BLOCK directly
ldlm_clear_cbpending||deprecated, use LDLM_FL_CBPENDING directly
ldlm_clear_cleaned||deprecated, use LDLM_FL_CLEANED directly
ldlm_clear_converting||deprecated, use LDLM_FL_CONVERTING directly
ldlm_clear_cos_incompat||deprecated, use LDLM_FL_COS_INCOMPAT directly
ldlm_clear_cp_reqd||deprecated, use LDLM_FL_CP_REQD directly
ldlm_clear_deny_on_contention||deprecated, use LDLM_FL_DENY_ON_CONTENTION directly
ldlm_clear_destroyed||deprecated, use LDLM_FL_DESTROYED directly
ldlm_clear_discard_data||deprecated, use LDLM_FL_DISCARD_DATA directly
ldlm_clear_do_not_expand||deprecated, use LDLM_FL_DO_NOT_EXPAND directly
ldlm_clear_excl||deprecated, use LDLM_FL_EXCL directly
ldlm_clear_failed||deprecated, use LDLM_FL_FAILED directly
ldlm_clear_fail_loc||deprecated, use LDLM_FL_FAIL_LOC directly
ldlm_clear_fail_notified||deprecated, use LDLM_FL_FAIL_NOTIFIED directly
ldlm_clear_flock_deadlock||deprecated, use LDLM_FL_FLOCK_DEADLOCK directly
ldlm_clear_has_intent||deprecated, use LDLM_FL_HAS_INTENT directly
ldlm_clear_intent_only||deprecated, use LDLM_FL_INTENT_ONLY directly
ldlm_clear_kms_ignore||deprecated, use LDLM_FL_KMS_IGNORE directly
ldlm_clear_local||deprecated, use LDLM_FL_LOCAL directly
ldlm_clear_local_only||deprecated, use LDLM_FL_LOCAL_ONLY directly
ldlm_clear_lock_changed||deprecated, use LDLM_FL_LOCK_CHANGED directly
ldlm_clear_lvb_cached||deprecated, use LDLM_FL_LVB_CACHED directly
ldlm_clear_lvb_ready||deprecated, use LDLM_FL_LVB_READY directly
ldlm_clear_no_lru||deprecated, use LDLM_FL_NO_LRU directly
ldlm_clear_no_timeout||deprecated, use LDLM_FL_NO_TIMEOUT directly
ldlm_clear_ns_srv||deprecated, use LDLM_FL_NS_SRV directly
ldlm_clear_replay||deprecated, use LDLM_FL_REPLAY directly
ldlm_clear_res_locked||deprecated, use LDLM_FL_RES_LOCKED directly
ldlm_clear_specualtive||deprecated, use LDLM_FL_SPECULATIVE directly
ldlm_clear_test_lock||deprecated, use LDLM_FL_TEST_LOCK directly
ldlm_clear_waited||deprecated, use LDLM_FL_WAITED directly
ldlm_clear_wait_noreproc||deprecated, use LDLM_FL_WAIT_NOREPROC directly
ldlm_error_t||enum ldlm_error
ldlm_is_ast_discard_data||deprecated, use LDLM_FL_DISCARD_DATA directly
ldlm_is_ast_sent||deprecated, use LDLM_FL_AST_SENT directly
ldlm_is_atomic_cb||deprecated, use LDLM_FL_ATOMIC_CB directly
ldlm_is_bl_ast||deprecated, use LDLM_FL_BL_AST directly
ldlm_is_bl_done||deprecated, use LDLM_FL_BL_DONE directly
ldlm_is_block_conv||deprecated, use LDLM_FL_BLOCK_CONV directly
ldlm_is_block_granted||deprecated, use LDLM_FL_BLOCK_GRANTED directly
ldlm_is_block_nowait||deprecated, use LDLM_FL_BLOCK_NOWAIT directly
ldlm_is_block_wait||deprecated, use LDLM_FL_BLOCK_WAIT directly
ldlm_is_cancel||deprecated, use LDLM_FL_CANCEL directly
ldlm_is_canceling||deprecated, use LDLM_FL_CANCELING directly
ldlm_is_cancel_on_block||deprecated, use LDLM_FL_CANCEL_ON_BLOCK directly
ldlm_is_cbpending||deprecated, use LDLM_FL_CBPENDING directly
ldlm_is_cleaned||deprecated, use LDLM_FL_CLEANED directly
ldlm_is_converting||deprecated, use LDLM_FL_CONVERTING directly
ldlm_is_cos_enabled||deprecated, use LDLM_FL_COS_ENABLED directly
ldlm_is_cos_incompat||deprecated, use LDLM_FL_COS_INCOMPAT directly
ldlm_is_cp_reqd||deprecated, use LDLM_FL_CP_REQD directly
ldlm_is_deny_on_contention||deprecated, use LDLM_FL_DENY_ON_CONTENTION directly
ldlm_is_destroyed||deprecated, use LDLM_FL_DESTROYED directly
ldlm_is_discard_data||deprecated, use LDLM_FL_DISCARD_DATA directly
ldlm_is_do_not_expand||deprecated, use LDLM_FL_DO_NOT_EXPAND directly
ldlm_is_excl||deprecated, use LDLM_FL_EXCL directly
ldlm_is_failed||deprecated, use LDLM_FL_FAILED directly
ldlm_is_fail_loc||deprecated, use LDLM_FL_FAIL_LOC directly
ldlm_is_fail_notified||deprecated, use LDLM_FL_FAIL_NOTIFIED directly
ldlm_is_flock_deadlock||deprecated, use LDLM_FL_FLOCK_DEADLOCK directly
ldlm_is_has_intent||deprecated, use LDLM_FL_HAS_INTENT directly
ldlm_is_intent_only||deprecated, use LDLM_FL_INTENT_ONLY directly
ldlm_is_kms_ignore||deprecated, use LDLM_FL_KMS_IGNORE directly
ldlm_is_local||deprecated, use LDLM_FL_LOCAL directly
ldlm_is_local_only||deprecated, use LDLM_FL_LOCAL_ONLY directly
ldlm_is_lock_changed||deprecated, use LDLM_FL_LOCK_CHANGED directly
ldlm_is_lvb_cached||deprecated, use LDLM_FL_LVB_CACHED directly
ldlm_is_lvb_ready||deprecated, use LDLM_FL_LVB_READY directly
ldlm_is_ndelay||deprecated, use LDLM_FL_NDELAY directly
ldlm_is_no_lru||deprecated, use LDLM_FL_NO_LRU directly
ldlm_is_no_timeout||deprecated, use LDLM_FL_NO_TIMEOUT directly
ldlm_is_ns_srv||deprecated, use LDLM_FL_NS_SRV directly
ldlm_is_replay||deprecated, use LDLM_FL_REPLAY directly
ldlm_is_res_locked||deprecated, use LDLM_FL_RES_LOCKED directly
ldlm_is_server_lock||deprecated, use LDLM_FL_SERVER_LOCK directly
ldlm_is_speculative||deprecated, use LDLM_FL_SPECULATIVE directly
ldlm_is_test_lock||deprecated, use LDLM_FL_TEST_LOCK directly
ldlm_is_waited||deprecated, use LDLM_FL_WAITED directly
ldlm_is_wait_noreproc||deprecated, use LDLM_FL_WAIT_NOREPROC directly
ldlm_mode_t||enum ldlm_mode
ldlm_ns_hash_def_t||struct ldlm_ns_hash_def
ldlm_ns_type_t||enum ldlm_ns_type
ldlm_policy_data_t||enum ldlm_policy_data
ldlm_policy_res_t||enum ldlm_policy_res
ldlm_set_ast_discard_data||deprecated, use LDLM_FL_AST_DISCARD_DATA directly
ldlm_set_ast_sent||deprecated, use LDLM_FL_AST_SENT directly
ldlm_set_atomic_cb||deprecated, use LDLM_FL_ATOMIC_CB directly
ldlm_set_bl_ast||deprecated, use LDLM_FL_BL_AST directly
ldlm_set_bl_done||deprecated, use LDLM_FL_BL_DONE directly
ldlm_set_block_conv||deprecated, use LDLM_FL_BLOCK_CONV directly
ldlm_set_block_granted||deprecated, use LDLM_FL_BLOCK_GRANTED directly
ldlm_set_block_nowait||deprecated, use LDLM_FL_BLOCK_NOWAIT directly
ldlm_set_block_wait||deprecated, use LDLM_FL_BLOCK_WAIT directly
ldlm_set_cancel||deprecated, use LDLM_FL_CANCEL directly
ldlm_set_canceling||deprecated, use LDLM_FL_CANCELING directly
ldlm_set_cancel_on_block||deprecated, use LDLM_FL_CANCEL_ON_BLOCK directly
ldlm_set_cbpending||deprecated, use LDLM_FL_CBPENDING directly
ldlm_set_cleaned||deprecated, use LDLM_FL_CLEANED directly
ldlm_set_converting||deprecated, use LDLM_FL_CONVERTING directly
ldlm_set_cos_enabled||deprecated, use LDLM_FL_COS_ENABLED directly
ldlm_set_cos_incompat||deprecated, use LDLM_FL_COS_INCOMPAT directly
ldlm_set_cp_reqd||deprecated, use LDLM_FL_CP_REQD directly
ldlm_set_deny_on_contention||deprecated, use LDLM_FL_DENY_ON_CONTENTION directly
ldlm_set_destroyed||deprecated, use LDLM_FL_DESTROYED directly
ldlm_set_discard_data||deprecated, use LDLM_FL_DISCARD_DATA directly
ldlm_set_do_not_expand||deprecated, use LDLM_FL_DO_NOT_EXPAND directly
ldlm_set_excl||deprecated, use LDLM_FL_EXCL directly
ldlm_set_failed||deprecated, use LDLM_FL_FAILED directly
ldlm_set_fail_loc||deprecated, use LDLM_FL_FAIL_LOC directly
ldlm_set_fail_notified||deprecated, use LDLM_FL_FAIL_NOTIFIED directly
ldlm_set_flock_deadlock||deprecated, use LDLM_FL_FLOCK_DEADLOCK directly
ldlm_set_has_intent||deprecated, use LDLM_FL_HAS_INTENT directly
ldlm_set_intent_only||deprecated, use LDLM_FL_INTENT_ONLY directly
ldlm_set_kms_ignore||deprecated, use LDLM_FL_KMS_IGNORE directly
ldlm_set_local||deprecated, use LDLM_FL_LOCAL directly
ldlm_set_local_only||deprecated, use LDLM_FL_LOCAL_ONLY directly
ldlm_set_lock_changed||deprecated, use LDLM_FL_LOCK_CHANGED directly
ldlm_set_lvb_cached||deprecated, use LDLM_FL_LVB_CACHED directly
ldlm_set_lvb_ready||deprecated, use LDLM_FL_LVB_READY directly
ldlm_set_ndelay||deprecated, use LDLM_FL_NDELAY directly
ldlm_set_no_lru||deprecated, use LDLM_FL_NO_LRU directly
ldlm_set_no_timeout||deprecated, use LDLM_FL_NO_TIMEOUT directly
ldlm_set_ns_srv||deprecated, use LDLM_FL_NS_SRV directly
ldlm_set_replay||deprecated, use LDLM_FL_REPLAY directly
ldlm_set_res_locked||deprecated, use LDLM_FL_RES_LOCKED directly
ldlm_set_server_lock||deprecated, use LDLM_FL_SERVER_LOCK directly
ldlm_set_speculative||deprecated, use LDLM_FL_SPECULATIVE directly
ldlm_set_test_lock||deprecated, use LDLM_FL_TEST_LOCK directly
ldlm_set_waited||deprecated, use LDLM_FL_WAITED directly
ldlm_set_wait_noreproc||deprecated, use LDLM_FL_WAIT_NOREPROC directly
ldlm_side_t||enum ldlm_side
LDLM_LOCK_PUT||ldlm_lock_put
LDLM_LOCK_RELEASE||ldlm_lock_put
LDLM_LOCK_GET||ldlm_lock_get
LDLM_TYPE_MAX||LDLM_TYPE_END
ldlm_type_t||enum ldlm_type
ldlm_wire_policy_data_t||union ldlm_wire_policy_data
libcfs_debug_vmsg2||libcfs_debug_msg
li_advice||lio_advice
li_end||lio_end
li_extent||lai_extent
li_fid||lio_fid
li_opc||lai_opc
li_start||lio_start
lnet_acceptor_connreq_t||struct lnet_acceptor_connreq
lnet_counters_t||struct lnet_counters
lnet_handle_wire_t||struct lnet_handle_wire
lnet_hdr_t||struct lnet_hdr
lnet_magicversion_t||struct lnet_magicversion
lnet_ni_status_t||struct lnet_ni_status
lnet_ping_info_t||struct lnet_ping_info
lnet_process_id_packed_t||struct lnet_process_id_packed
LOOKUP_CONTINUE||LOOKUP_PARENT
LPD64||%lld
LPLD||%ld
LPLU||%lu
LPLX||%#lx
LPO64||%#llo
LPPID||%d
lprocfs_str_to_s64||kstrtoxxx_from_user
lprocfs_str_with_units_to_u64||sysfs_memparse using sysfs/kernbuf
lprocfs_str_with_units_to_s64||sysfs_memparse using sysfs/kernbuf
LPROC_SEQ_FOPS||LUSTRE_RW_ATTR
LPROC_SEQ_FOPS_RO_TYPE||LUSTRE_RO_ATTR
LPROC_SEQ_FOPS_RO||LUSTRE_RO_ATTR
LPROC_SEQ_FOPS_RW_TYPE||LUSTRE_RW_ATTR
LPROC_SEQ_FOPS_WR_ONLY||LUSTRE_WO_ATTR
LUSTRE_RO_ATTR||create Documentation/man4/ page for this parameter
LUSTRE_RW_ATTR||create Documentation/man4/ page for this parameter
LUSTRE_WO_ATTR||create Documentation/man4/ page for this parameter
lu_str_to_s64||sysfs_memparse
l_wait_event||wait_event_idle
l_wait_event_exclusive||l_wait_event_abortable_exclusive
l_wait_event_exclusive_head||wait_event_idle_exclusive_lifo_timeout
l_wait_condition||wait_event_idle
MAX\(||max_t
MIN\(||min_t
mdo2fid||mdd_object_fid
mktemp||mkstemp
nla_strlcpy||nla_strscpy
OBD_FAILED||CFS_FAILED
OBD_FAIL_CHECK||CFS_FAIL_CHECK
OBD_FAIL_CHECK_ORSET||CFS_FAIL_CHECK_ORSET
OBD_FAIL_CHECK_RESET||CFS_FAIL_CHECK_RESET
OBD_FAIL_CHECK_VALUE||CFS_FAIL_CHECK_VALUE
OBD_FAIL_ONCE||CFS_FAIL_ONCE
OBD_FAIL_PRECHECK||CFS_FAIL_PRECHECK
OBD_FAIL_RETURN||CFS_FAIL_RETURN
OBD_FAIL_TIMEOUT||CFS_FAIL_TIMEOUT
OBD_FAIL_TIMEOUT_MS||CFS_FAIL_TIMEOUT_MS
OBD_FAIL_TIMEOUT_ORSET||CFS_FAIL_TIMEOUT_ORSET
OBD_RACE||CFS_RACE
OS_STATE_DEGRADED||OS_STATFS_DEGRADED
OS_STATE_READONLY||OS_STATFS_READONLY
OS_STATE_NOPRECREATE||OS_STATFS_NOCREATE
OS_STATE_ENOSPC||OS_STATFS_ENOSPC
OS_STATE_ENOINO||OS_STATFS_ENOINO
OS_STATE_SUM||OS_STATFS_SUM
OS_STATE_NONROT||OS_STATFS_NONROT
OS_STATFS_NOPRECREATE||OS_STATFS_NOCREATE
page_cache_get||get_page
PAGE_CACHE_MASK||PAGE_MASK
page_cache_release||put_page
PAGE_CACHE_SHIFT||PAGE_SHIFT
PAGE_CACHE_SIZE||PAGE_SIZE
PNAME||encode_fn_luname
prandom_u32||get_random_u32
prandom_u32_max||get_random_u32_below
ptlrpc_req_finished||ptlrpc_req_put
return seq_printf||seq_printf
setup_timer||cfs_timer_setup
= seq_printf||seq_printf
sprintf||snprintf
strcat||strncat
strcpy||strncpy
strlcpy||strscpy
struct timeval||struct timespec64
tempnam||mkstemp
time_t||timeout_t
timer_setup||cfs_timer_setup
version_code.*2.1[7-9]||version 2.16.x should be used
vui_tot_count||vui_tot_bytes
wait_queue_t||wait_queue_entry_t
xa_insert||ll_xa_insert
yaml_emitter_delete||yaml_emitter_cleanup
yaml_parser_delete||yaml_parser_cleanup
