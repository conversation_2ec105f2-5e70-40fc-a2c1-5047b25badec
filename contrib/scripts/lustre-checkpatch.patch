From 9b8a6c6384cc6863cdda7f22deddad56ac20b451 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Wed, 21 Feb 2024 18:35:37 +0000
Subject: [PATCH] LU-6142 contrib: Lustre checkpatch.pl

v6.13-rc3-g231825b2e1ff

Signed-off-by: <PERSON> <<EMAIL>>
Change-Id: I4df9cff42d7ce377bdd221f99e5e66f4ee4524ee
---
 contrib/scripts/checkpatch.pl | 151 ++++++++++++++++++++++++++++------
 1 file changed, 124 insertions(+), 27 deletions(-)

diff --git a/contrib/scripts/checkpatch.pl b/contrib/scripts/checkpatch.pl
index 9eed3683ad..139061fdbd 100755
--- a/contrib/scripts/checkpatch.pl
+++ b/contrib/scripts/checkpatch.pl
@@ -7,6 +7,9 @@
 # (c) 2008-2010 <PERSON> <<EMAIL>>
 # (c) 2010-2018 <PERSON> <<EMAIL>>
 
+# Based on linux/scripts/checkpatch.pl v6.13-rc3-g231825b2e1ff with
+# some additional Lustre-specific checks.
+
 use strict;
 use warnings;
 use POSIX;
@@ -26,8 +29,8 @@ my $quiet = 0;
 my $verbose = 0;
 my %verbose_messages = ();
 my %verbose_emitted = ();
-my $tree = 1;
-my $chk_signoff = 1;
+my $tree = 0;
+my $chk_signoff = 0;
 my $chk_fixes_tag = 1;
 my $chk_patch = 1;
 my $tst_only;
@@ -57,11 +60,12 @@ my %ignore_type = ();
 my @ignore = ();
 my $help = 0;
 my $configuration_file = ".checkpatch.conf";
-my $max_line_length = 100;
+my $max_line_length = 80;
 my $ignore_perl_version = 0;
 my $minimum_perl_version = 5.10.0;
 my $min_conf_desc_length = 4;
 my $spelling_file = "$D/spelling.txt";
+my $spelling_file_userspace = "$D/userspace_spelling.txt";
 my $codespell = 0;
 my $codespellfile = "/usr/share/codespell/dictionary.txt";
 my $user_codespellfile = "";
@@ -69,7 +73,7 @@ my $conststructsfile = "$D/const_structs.checkpatch";
 my $docsfile = "$D/../Documentation/dev-tools/checkpatch.rst";
 my $typedefsfile;
 my $color = "auto";
-my $allow_c99_comments = 1; # Can be overridden by --ignore C99_COMMENT_TOLERANCE
+my $allow_c99_comments = 0; # Can be overridden by --ignore C99_COMMENT_TOLERANCE
 # git output parsing needs US English output, so first set backtick child process LANGUAGE
 my $git_command ='export LANGUAGE=en_US.UTF-8; git';
 my $tabsize = 8;
@@ -599,9 +603,12 @@ our $logFunctions = qr{(?x:
 	(?:[a-z0-9]+_){1,2}(?:printk|emerg|alert|crit|err|warning|warn|notice|info|debug|dbg|vdbg|devel|cont|WARN)(?:_ratelimited|_once|)|
 	TP_printk|
 	WARN(?:_RATELIMIT|_ONCE|)|
+	CDEBUG|CERROR|CNETERR|CEMERG|CL_LOCK_DEBUG|CWARN|DEBUG_REQ|LCONSOLE_[A-Z]*|
+	CERROR_SLOW|CWARN_SLOW|
 	panic|
 	MODULE_[A-Z_]+|
-	seq_vprintf|seq_printf|seq_puts
+	seq_vprintf|seq_printf|seq_puts|
+	printf|fprintf
 )};
 
 our $allocFunctions = qr{(?x:
@@ -980,6 +987,31 @@ if ($codespell) {
 
 $misspellings = join("|", sort keys %spelling_fix) if keys %spelling_fix;
 
+# Load common spelling mistakes and build regular expression list.
+my $misspellings_user;
+my %spelling_fix_user;
+
+if (open(my $spelling, '<', $spelling_file_userspace)) {
+	while (<$spelling>) {
+		my $line = $_;
+
+		$line =~ s/\s*\n?$//g;
+		$line =~ s/^\s*//g;
+
+		next if ($line =~ m/^\s*#/);
+		next if ($line =~ m/^\s*$/);
+
+		my ($suspect, $fix) = split(/\|\|/, $line);
+
+		$spelling_fix_user{$suspect} = $fix;
+	}
+	close($spelling);
+} else {
+	warn "No typos will be found - file '$spelling_file_userspace': $!\n";
+}
+
+$misspellings_user = join("|", sort keys %spelling_fix_user) if keys %spelling_fix_user;
+
 sub read_words {
 	my ($wordsRef, $file) = @_;
 
@@ -1978,6 +2010,7 @@ sub ctx_locate_comment {
 	for (my $linenr = $first_line; $linenr < $end_line; $linenr++) {
 		my $line = $rawlines[$linenr - 1];
 		#warn "           $line\n";
+		next if ($line =~ /^-/); # ignore lines removed by patch
 		if ($linenr == $first_line and $line =~ m@^.\s*\*@) {
 			$in_comment = 1;
 		}
@@ -2680,6 +2713,7 @@ sub process {
 	my $comment_edge = 0;
 	my $first_line = 0;
 	my $p1_prefix = '';
+	my $manfile = '';
 
 	my $prev_values = 'E';
 
@@ -2880,6 +2914,17 @@ sub process {
 			$found_file = 1;
 		}
 
+#handle man pages
+		if ($line =~ /^diff --git.*?(\S+\.[1-8])$/ ||
+			$line =~ /^\+\+\+\s+(\S+\.[1-8])$/) {
+				if ($manfile !~ $1) {
+					$manfile = $1;
+					local @ARGV = $manfile;
+					system($^X, "contrib/scripts/checkpatch-man.pl", @ARGV);
+					$found_file = 1;
+				}
+			}
+
 #make up the handle for any error we report on this line
 		if ($showfile) {
 			$prefix = "$realfile:$realline: "
@@ -3254,15 +3299,6 @@ sub process {
 			     "A patch subject line should describe the change not the tool that found it\n" . $herecurr);
 		}
 
-# Check for Gerrit Change-Ids not in any patch context
-		if ($realfile eq '' && !$has_patch_separator && $line =~ /^\s*change-id:/i) {
-			if (ERROR("GERRIT_CHANGE_ID",
-			          "Remove Gerrit Change-Id's before submitting upstream\n" . $herecurr) &&
-			    $fix) {
-				fix_delete_line($fixlinenr, $rawline);
-			}
-		}
-
 # Check if the commit log is in a possible stack dump
 		if ($in_commit_log && !$commit_log_possible_stack_dump &&
 		    ($line =~ /^\s*(?:WARNING:|BUG:)/ ||
@@ -3489,16 +3525,35 @@ sub process {
 		}
 
 # Check for various typo / spelling mistakes
-		if (defined($misspellings) &&
-		    ($in_commit_log || $line =~ /^(?:\+|Subject:)/i)) {
-			while ($rawline =~ /(?:^|[^\w\-'`])($misspellings)(?:[^\w\-'`]|$)/gi) {
+		if (($realfile =~ m@^(lustre/utils/|lustre/tests/|lnet/utils/|libcfs/libcfs/util/)@) &&
+		    defined($misspellings_user) && ($in_commit_log || $line =~ /^(?:\+|Subject:)/i)) {
+			while ($rawline =~ /(?:^|[^\w\-'`])($misspellings_user)(?:[^\w\-'`]|$)/g) {
+				my $typo = $1;
+				my $blank = copy_spacing($rawline);
+				my $ptr = substr($blank, 0, $-[1]) . "^" x length($typo);
+				my $hereptr = "$hereline$ptr\n";
+				my $typo_fix = $spelling_fix_user{$typo};
+				if (!defined($typo_fix)) {
+					$typo_fix = "CHECKPATCH ERROR";
+				}
+				my $msg_level = \&WARN;
+				$msg_level = \&CHK if ($file);
+				if (&{$msg_level}("TYPO_SPELLING",
+						  "'$typo' may be misspelled - perhaps '$typo_fix'?\n" . $hereptr) &&
+				    $fix) {
+					$fixed[$fixlinenr] =~ s/(^|[^A-Za-z@])($typo)($|[^A-Za-z@])/$1$typo_fix$3/;
+				}
+			}
+		} elsif (defined($misspellings) && ($in_commit_log || $line =~ /^(?:\+|Subject:)/i)) {
+			while ($rawline =~ /(?:^|[^\w\-'`])($misspellings)(?:[^\w\-'`]|$)/g) {
 				my $typo = $1;
 				my $blank = copy_spacing($rawline);
 				my $ptr = substr($blank, 0, $-[1]) . "^" x length($typo);
 				my $hereptr = "$hereline$ptr\n";
-				my $typo_fix = $spelling_fix{lc($typo)};
-				$typo_fix = ucfirst($typo_fix) if ($typo =~ /^[A-Z]/);
-				$typo_fix = uc($typo_fix) if ($typo =~ /^[A-Z]+$/);
+				my $typo_fix = $spelling_fix{$typo};
+				if (!defined($typo_fix)) {
+					$typo_fix = "CHECKPATCH ERROR";
+				}
 				my $msg_level = \&WARN;
 				$msg_level = \&CHK if ($file);
 				if (&{$msg_level}("TYPO_SPELLING",
@@ -3842,6 +3897,11 @@ sub process {
 			    length(expand_tabs(substr($line, 1, length($line) - length($1) - 1))) <= $max_line_length) {
 				$msg_type = "";
 
+			# a Lustre message that contains embedded formatting
+			} elsif ($line =~ /^\+\s*(?:$logFunctions\s*\()?($String(?:DFID|DOSTID)$String\s*(?:|,|\)\s*;)?\s*)$/ &&
+				 length(expand_tabs(substr($line, 1, length($line) - length($1) - 1))) <= $max_line_length) {
+				$msg_type = ""
+
 			# lines with only strings (w/ possible termination)
 			# #defines with only strings
 			} elsif ($line =~ /^\+\s*$String\s*(?:\s*|,|\)\s*;)\s*$/ ||
@@ -5612,6 +5672,8 @@ sub process {
 #	avoid cases like "foo + BAR < baz"
 #	only fix matches surrounded by parentheses to avoid incorrect
 #	conversions like "FOO < baz() + 5" being "misfixed" to "baz() > FOO + 5"
+# Exceptions:
+# 01.	LUSTRE_VERSION_CODE upper-case constant on left side.
 		if ($perl_version_ok &&
 		    $line =~ /^\+(.*)\b($Constant|[A-Z_][A-Z0-9_]*)\s*($Compare)\s*($LvalOrFunc)/) {
 			my $lead = $1;
@@ -5621,6 +5683,7 @@ sub process {
 			my $newcomp = $comp;
 			if ($lead !~ /(?:$Operators|\.)\s*$/ &&
 			    $to !~ /^(?:Constant|[A-Z_][A-Z0-9_]*)$/ &&
+			    $const !~ /LUSTRE_VERSION_CODE/ &&
 			    WARN("CONSTANT_COMPARISON",
 				 "Comparisons should place the constant on the right side of the test\n" . $herecurr) &&
 			    $fix) {
@@ -6234,6 +6297,47 @@ sub process {
 			}
 		}
 
+# Lustre try to replace assertions with error handling
+		if ($line =~ /\bLASSERTF?\s*\(/) {
+		    WARN("LASSERT",
+			 "Try to replace assertions with error handling\n" .
+			 $herecurr);
+		}
+
+# Lustre minimize new CERROR messages
+		if ($line =~ /\b(CEMERG|CERROR|CNETERR|CWARN|LCONSOLE)\s*\(/) {
+			if ($rawline !~ /\(\"\%s: /) {
+				WARN("CERROR_DEV",
+				     "Console messages should start with '%s:' to print device name\n" . $herecurr)
+			}
+
+			# Check for "rc = %d" or "rc = %ld" or "rc = %zd"
+			# at the end of errors
+			if ($line !~ /LCONSOLE/ &&
+			    $rawline !~ /: rc = \%l|z?d\\n\",/) {
+				WARN("CERROR_RET",
+				     "Console messages should end with ': rc = %[lz]d'\n" . $herecurr);
+			}
+
+			# This is fine as we are only matching the first part.
+			if ($line =~ /(CEMERG|CERROR|CNETERR)/) {
+				WARN("CERROR",
+				     "Review error messages to ensure they are useful, and not just status/debug\n" . $herecurr);
+			}
+		}
+
+# Lustre avoid unlimited message printing to the console
+		if ($line =~ /CDEBUG\((D_ERROR|D_WARNING|D_CONSOLE|[a-z])/) {
+			WARN("CDEBUG_LIMIT",
+			     "CDEBUG does not rate-limit console messages, use CDEBUG_LIMIT\n". $herecurr);
+		}
+
+# Lustre don't allow GPLv3 license files
+		if ($rawline =~ /version 3/) {
+			WARN("GPLV3",
+			     "Using GPLv3 is usually wrong\n" . $herecurr);
+		}
+
 # check for single line unbalanced braces
 		if ($sline =~ /^.\s*\}\s*else\s*$/ ||
 		    $sline =~ /^.\s*else\s*\{\s*$/) {
@@ -6916,13 +7020,6 @@ sub process {
 						$bad_specifier = $specifier;
 						last;
 					}
-					if ($extension eq "x" && !defined($stat_real)) {
-						if (!defined($stat_real)) {
-							$stat_real = get_stat_real($linenr, $lc);
-						}
-						WARN("VSPRINTF_SPECIFIER_PX",
-						     "Using vsprintf specifier '\%px' potentially exposes the kernel memory layout, if you don't really need the address please consider using '\%p'.\n" . "$here\n$stat_real\n");
-					}
 				}
 				if ($bad_specifier ne "") {
 					my $stat_real = get_stat_real($linenr, $lc);
-- 
2.39.5

