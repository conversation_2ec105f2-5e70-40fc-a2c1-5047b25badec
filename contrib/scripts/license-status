#!/bin/bash
# SPDX-License-Identifier: GPL-2.0-only

#
# Copyright (c) 2001, 2010, Oracle and/or its affiliates. All rights reserved.
#
# Copyright (c) 2023-2025, Amazon and/or its affiliates. All rights reserved.
# Use is subject to license terms.
#

#
# This file is part of Lustre, http://www.lustre.org/
#
# Display the license status of files in the current directory
#
# Author: <PERSON> <<EMAIL>>, 2001-09-27
# Author: <PERSON> <<EMAIL>>, 2023-04-03
#

export files="${1:-$(pwd)}"
export file_paths=$(git ls-files "$files" | grep -v -f <(cat <<EOF
Documentation
LICENSES
gitignore
COPYING
mailmap
MAINTAINERS
README
requirements
SECURITY
kernel_patches
crypto_patches
EOF
))

for file in $file_paths; do
	if head -20 "$file" | grep -Ee 'SPDX' > /dev/null; then
		license="$(grep SPDX "$file" | awk '{ print $3; exit; }')"
		printf "%-10s %-15s %-s\n" "SPDX" "$license" "$file"
	elif head -20 "$file" | grep -Ee 'GNU General Public License' > /dev/null; then
		printf "%-10s %-15s %-s\n" "guess" "gpled" "$file"
	elif head -20 "$file" | grep -Ee '\([Cc]\)' > /dev/null; then
		printf "%-10s %-15s %-s\n" "guess" "copyright" "$file"
	else
		printf "%-10s %-15s %-s\n" "guess" "unknown" "$file"
	fi
done | sort
