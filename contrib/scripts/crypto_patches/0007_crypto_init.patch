Make llcrypt_init visible outside of llcrypt.

--- a/libcfs/include/libcfs/crypto/llcrypt.h
+++ b/libcfs/include/libcfs/crypto/llcrypt.h
@@ -105,6 +105,7 @@ static inline void llcrypt_handle_d_move
 }
 
 /* crypto.c */
+extern int __init llcrypt_init(void);
 extern void llcrypt_enqueue_decrypt_work(struct work_struct *);
 extern struct llcrypt_ctx *llcrypt_get_ctx(gfp_t);
 extern void llcrypt_release_ctx(struct llcrypt_ctx *);
--- a/libcfs/libcfs/crypto/crypto.c
+++ b/libcfs/libcfs/crypto/crypto.c
@@ -476,7 +476,7 @@ void llcrypt_msg(const struct inode *ino
 /**
  * llcrypt_init() - Set up for fs encryption.
  */
-static int __init llcrypt_init(void)
+int __init llcrypt_init(void)
 {
 	int err = -ENOMEM;
 
@@ -517,4 +517,3 @@ fail_free_queue:
 fail:
 	return err;
 }
-late_initcall(llcrypt_init)
